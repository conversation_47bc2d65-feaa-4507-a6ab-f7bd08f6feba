"""
分析古诗文网的fanyiShow函数机制
"""
import requests
import re
from bs4 import BeautifulSoup

def analyze_fanyi_mechanism():
    """分析fanyiShow函数的工作机制"""
    url = "https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    response = requests.get(url, headers=headers)
    response.encoding = 'utf-8'
    
    print(f"页面获取成功，长度: {len(response.text)}")
    
    # 保存完整HTML用于分析
    with open('full_page_analysis.html', 'w', encoding='utf-8') as f:
        f.write(response.text)
    
    # 查找fanyiShow函数定义
    fanyi_function_pattern = r'function\s+fanyiShow\s*\([^)]*\)\s*\{[^}]*\}'
    fanyi_matches = re.findall(fanyi_function_pattern, response.text, re.DOTALL)
    
    print(f"\n=== fanyiShow函数定义 ===")
    for i, match in enumerate(fanyi_matches):
        print(f"函数 {i+1}: {match[:200]}...")
    
    # 查找所有fanyiShow调用
    fanyi_calls = re.findall(r'fanyiShow\([^)]+\)', response.text)
    print(f"\n=== fanyiShow函数调用 ===")
    for call in fanyi_calls:
        print(f"调用: {call}")
    
    # 查找可能的AJAX请求URL
    ajax_patterns = [
        r'["\']([^"\']*fanyi[^"\']*)["\']',
        r'["\']([^"\']*ajax[^"\']*)["\']',
        r'url\s*:\s*["\']([^"\']+)["\']'
    ]
    
    print(f"\n=== 可能的AJAX URL ===")
    for pattern in ajax_patterns:
        matches = re.findall(pattern, response.text, re.IGNORECASE)
        for match in matches:
            if 'fanyi' in match.lower() or 'ajax' in match.lower():
                print(f"发现URL: {match}")
    
    # 查找隐藏的div内容
    soup = BeautifulSoup(response.text, 'lxml')
    
    # 查找所有包含fanyi的div
    fanyi_divs = soup.find_all('div', id=re.compile(r'fanyi\d+'))
    print(f"\n=== Fanyi Div分析 ===")
    for div in fanyi_divs:
        print(f"Div ID: {div.get('id')}")
        print(f"Style: {div.get('style', '')}")
        print(f"内容长度: {len(div.get_text())}")
        print(f"内容预览: {div.get_text()[:100]}...")
        print("-" * 50)
    
    # 查找script标签中的数据
    scripts = soup.find_all('script')
    print(f"\n=== Script标签分析 ===")
    for i, script in enumerate(scripts):
        if script.string and ('fanyi' in script.string.lower() or 'innerHTML' in script.string):
            print(f"Script {i+1} (包含fanyi/innerHTML):")
            print(script.string[:300] + "..." if len(script.string) > 300 else script.string)
            print("-" * 50)
    
    # 查找特定的译文注释区域
    contyishang_divs = soup.find_all('div', class_='contyishang')
    print(f"\n=== 译文注释区域分析 ===")
    for i, div in enumerate(contyishang_divs):
        print(f"区域 {i+1}:")
        print(f"HTML: {str(div)[:500]}...")
        print("-" * 50)

if __name__ == "__main__":
    analyze_fanyi_mechanism()
