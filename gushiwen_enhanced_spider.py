"""
增强版古诗文网详情页面爬虫
使用requests获取页面，然后通过分析JavaScript和隐藏内容来获取完整的译文、注释和赏析
"""
import requests
import time
import re
from bs4 import BeautifulSoup
from loguru import logger
from models import SessionLocal, Poem
from config import SPIDER_CONFIG


class GuShiWenEnhancedSpider:
    """增强版古诗文网详情页面爬虫"""

    def __init__(self):
        self.session = requests.Session()
        self.delay = SPIDER_CONFIG['request_delay']
        self.setup_session()

    def setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)

    def get_page(self, url):
        """获取页面内容"""
        try:
            time.sleep(self.delay)
            response = self.session.get(url)
            response.raise_for_status()
            response.encoding = 'utf-8'
            logger.info(f"成功获取页面: {url}")
            return response
        except requests.RequestException as e:
            logger.error(f"获取页面失败 {url}: {e}")
            return None

    def extract_hidden_content(self, soup):
        """提取隐藏的完整内容"""
        hidden_content = {}

        # 查找所有script标签中的隐藏内容
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                script_content = script.string

                # 查找译文内容
                fanyi_match = re.search(
                    r'fanyi\d+.*?innerHTML\s*=\s*["\']([^"\']*)["\']', script_content, re.DOTALL)
                if fanyi_match:
                    fanyi_content = fanyi_match.group(1)
                    # 解码HTML实体
                    fanyi_content = fanyi_content.replace('\\n', '\n').replace(
                        '\\r', '').replace('\\"', '"').replace("\\'", "'")
                    hidden_content['fanyi'] = fanyi_content

                # 查找赏析内容
                shangxi_match = re.search(
                    r'shangxi\d+.*?innerHTML\s*=\s*["\']([^"\']*)["\']', script_content, re.DOTALL)
                if shangxi_match:
                    shangxi_content = shangxi_match.group(1)
                    shangxi_content = shangxi_content.replace('\\n', '\n').replace(
                        '\\r', '').replace('\\"', '"').replace("\\'", "'")
                    hidden_content['shangxi'] = shangxi_content

        return hidden_content

    def parse_poem_detail(self, url):
        """解析诗词详情页面"""
        response = self.get_page(url)
        if not response:
            return None

        soup = BeautifulSoup(response.text, 'lxml')

        # 提取基本信息
        poem_data = {
            'source_url': url,
            'title': '',
            'author': '',
            'dynasty': '',
            'content': '',
            'translation': '',
            'annotation': '',
            'appreciation': '',
            'background': '',
            'analysis': ''
        }

        try:
            # 1. 提取诗词标题 - 只取第一个（主要诗词的标题）
            title_elems = soup.find_all('h1')
            if title_elems:
                poem_data['title'] = title_elems[0].get_text().strip()
                logger.info(f"提取标题: {poem_data['title']}")

            # 2. 提取作者和朝代信息 - 只取第一个（主要诗词的信息）
            author_elems = soup.find_all('p', class_='source')
            if author_elems:
                author_elem = author_elems[0]  # 只取第一个
                author_links = author_elem.find_all('a')
                if author_links:
                    # 第一个链接是作者
                    author_text = author_links[0].get_text().strip()
                    if ' ' in author_text:
                        poem_data['author'] = author_text.split()[0]
                    else:
                        poem_data['author'] = author_text

                    # 第二个链接通常是朝代
                    if len(author_links) > 1:
                        dynasty_text = author_links[1].get_text().strip()
                        poem_data['dynasty'] = dynasty_text.replace(
                            '〔', '').replace('〕', '')

                logger.info(
                    f"提取作者: {poem_data['author']}, 朝代: {poem_data['dynasty']}")

            # 3. 提取诗词内容 - 只取第一个（主要诗词的内容）
            content_divs = soup.find_all('div', class_='contson')
            if content_divs:
                content_div = content_divs[0]  # 只取第一个
                content_html = str(content_div)
                content_html = content_html.replace(
                    '<br>', '\n').replace('<br/>', '\n')
                content_soup = BeautifulSoup(content_html, 'lxml')
                poem_data['content'] = content_soup.get_text().strip()
                logger.info(f"提取内容长度: {len(poem_data['content'])}")

            # 4. 提取隐藏的完整内容
            hidden_content = self.extract_hidden_content(soup)

            # 5. 提取译文及注释 - 先尝试显示的内容，再尝试隐藏的内容
            fanyi_divs = soup.find_all('div', id=re.compile(r'fanyi\d+'))
            translation_parts = []
            annotation_parts = []

            for fanyi_div in fanyi_divs:
                # 获取显示的内容
                paragraphs = fanyi_div.find_all('p')
                current_section = None

                for p in paragraphs:
                    text = p.get_text().strip()
                    if not text:
                        continue

                    if text.startswith('译文'):
                        current_section = 'translation'
                        content = text[2:].strip()
                        if content:
                            translation_parts.append(content)
                    elif text.startswith('注释'):
                        current_section = 'annotation'
                        content = text[2:].strip()
                        if content:
                            annotation_parts.append(content)
                    elif current_section == 'translation':
                        translation_parts.append(text)
                    elif current_section == 'annotation':
                        annotation_parts.append(text)
                    elif '译文' in text and '注释' in text:
                        # 处理译文和注释在同一段落的情况
                        parts = text.split('注释')
                        if len(parts) >= 2:
                            translation_text = parts[0].replace(
                                '译文', '').strip()
                            if translation_text:
                                translation_parts.append(translation_text)

                            annotation_text = parts[1].strip()
                            if annotation_text:
                                annotation_parts.append(annotation_text)

            # 如果没有获取到足够的内容，尝试从隐藏内容中提取
            if hidden_content.get('fanyi'):
                fanyi_html = hidden_content['fanyi']
                fanyi_soup = BeautifulSoup(fanyi_html, 'lxml')
                fanyi_text = fanyi_soup.get_text()

                if '译文' in fanyi_text and '注释' in fanyi_text:
                    parts = fanyi_text.split('注释')
                    if len(parts) >= 2:
                        translation_text = parts[0].replace('译文', '').strip()
                        if translation_text and len(translation_text) > len('\n'.join(translation_parts)):
                            translation_parts = [translation_text]

                        annotation_text = parts[1].strip()
                        if annotation_text and len(annotation_text) > len('\n'.join(annotation_parts)):
                            annotation_parts = [annotation_text]

            poem_data['translation'] = '\n'.join(translation_parts)
            poem_data['annotation'] = '\n'.join(annotation_parts)
            logger.info(f"提取译文长度: {len(poem_data['translation'])}")
            logger.info(f"提取注释长度: {len(poem_data['annotation'])}")

            # 6. 提取赏析、简析、创作背景
            shangxi_divs = soup.find_all('div', id=re.compile(r'shangxi\d+'))
            appreciation_parts = []
            analysis_parts = []
            background_parts = []

            for shangxi_div in shangxi_divs:
                h2_elem = shangxi_div.find('h2')
                if h2_elem:
                    section_title = h2_elem.get_text().strip()
                    # 获取显示的内容
                    paragraphs = shangxi_div.find_all('p')
                    for p in paragraphs:
                        text = p.get_text().strip()
                        if text and not text.startswith('参考资料'):
                            if '赏析' in section_title:
                                appreciation_parts.append(text)
                            elif '简析' in section_title:
                                analysis_parts.append(text)
                            elif '创作背景' in section_title or '背景' in section_title:
                                background_parts.append(text)

            # 如果没有获取到足够的赏析内容，尝试从隐藏内容中提取
            if hidden_content.get('shangxi'):
                shangxi_html = hidden_content['shangxi']
                shangxi_soup = BeautifulSoup(shangxi_html, 'lxml')
                shangxi_text = shangxi_soup.get_text().strip()

                if shangxi_text and len(shangxi_text) > len('\n'.join(appreciation_parts)):
                    appreciation_parts = [shangxi_text]

            # 查找其他sections
            sons_divs = soup.find_all('div', class_='sons')
            for section in sons_divs:
                h2_elem = section.find('h2')
                if h2_elem:
                    section_title = h2_elem.get_text().strip()
                    paragraphs = section.find_all('p')
                    for p in paragraphs:
                        text = p.get_text().strip()
                        if text and not text.startswith('参考资料'):
                            if '简析' in section_title:
                                analysis_parts.append(text)
                            elif '创作背景' in section_title or '背景' in section_title:
                                background_parts.append(text)

            poem_data['appreciation'] = '\n'.join(appreciation_parts)
            poem_data['analysis'] = '\n'.join(analysis_parts)
            poem_data['background'] = '\n'.join(background_parts)

            logger.info(f"提取赏析长度: {len(poem_data['appreciation'])}")
            logger.info(f"提取简析长度: {len(poem_data['analysis'])}")
            logger.info(f"提取创作背景长度: {len(poem_data['background'])}")

            return poem_data

        except Exception as e:
            logger.error(f"解析页面内容时出错: {e}")
            return None

    def save_to_db(self, poem_data):
        """保存诗词数据到数据库"""
        if not poem_data or not poem_data.get('title'):
            logger.warning("诗词数据为空或缺少标题，跳过保存")
            return False

        db = SessionLocal()
        try:
            # 检查是否已存在
            existing = db.query(Poem).filter(
                Poem.title == poem_data['title'],
                Poem.author == poem_data['author']
            ).first()

            if existing:
                # 更新现有记录
                existing.translation = poem_data['translation']
                existing.annotation = poem_data['annotation']
                existing.appreciation = poem_data['appreciation']
                db.commit()
                logger.info(
                    f"更新现有诗词: {poem_data['title']} - {poem_data['author']}")
                return True
            else:
                # 创建新记录
                poem = Poem(
                    title=poem_data['title'],
                    author=poem_data['author'],
                    dynasty=poem_data['dynasty'],
                    content=poem_data['content'],
                    translation=poem_data['translation'],
                    annotation=poem_data['annotation'],
                    appreciation=poem_data['appreciation'],
                    source_url=poem_data['source_url']
                )

                db.add(poem)
                db.commit()
                logger.success(
                    f"成功保存新诗词: {poem_data['title']} - {poem_data['author']}")
                return True

        except Exception as e:
            db.rollback()
            logger.error(f"保存诗词到数据库失败: {e}")
            return False
        finally:
            db.close()

    def crawl_poem(self, url):
        """爬取单个诗词页面"""
        logger.info(f"开始爬取诗词: {url}")

        poem_data = self.parse_poem_detail(url)
        if poem_data:
            success = self.save_to_db(poem_data)
            if success:
                logger.success(f"成功爬取并保存: {poem_data['title']}")
            return poem_data
        else:
            logger.error(f"爬取失败: {url}")
            return None


def main():
    """测试函数"""
    spider = GuShiWenEnhancedSpider()

    # 测试URL - 换一个有更多内容的页面
    test_url = "https://www.gushiwen.cn/shiwenv_87a776797882.aspx"

    result = spider.crawl_poem(test_url)
    if result:
        print("\n=== 爬取结果 ===")
        for key, value in result.items():
            if value:
                print(f"{key}: {value[:100]}..." if len(
                    str(value)) > 100 else f"{key}: {value}")


if __name__ == "__main__":
    main()
