#!/bin/bash

# 古诗文爬虫项目启动脚本

echo "=== 古诗文爬虫项目 ==="

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，请先运行 python3 -m venv venv"
    exit 1
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 检查参数
case "$1" in
    "test")
        echo "🧪 运行环境测试..."
        python test_setup.py
        ;;
    "spider")
        echo "🕷️ 启动爬虫..."
        python main.py
        ;;
    "db-test")
        echo "🗄️ 测试数据库连接..."
        python -c "from utils.db_utils import test_connection, get_table_counts; test_connection(); print('表记录数:', get_table_counts())"
        ;;
    "install")
        echo "📦 安装依赖..."
        pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt
        playwright install
        ;;
    *)
        echo "使用方法:"
        echo "  ./run.sh test      - 运行环境测试"
        echo "  ./run.sh spider    - 启动爬虫"
        echo "  ./run.sh db-test   - 测试数据库"
        echo "  ./run.sh install   - 安装依赖"
        ;;
esac
