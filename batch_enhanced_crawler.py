"""
批量测试增强版古诗文网爬虫
"""
from gushiwen_enhanced_spider import GuShiWenEnhancedSpider
from loguru import logger
import time

def main():
    """批量爬取测试"""
    spider = GuShiWenEnhancedSpider()
    
    # 测试URL列表
    test_urls = [
        "https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx",  # 殿前欢·次酸斋韵
        "https://www.gushiwen.cn/shiwenv_8dd7bf2f6a88.aspx",  # 春从天上来·罗绮深宫
        "https://www.gushiwen.cn/shiwenv_5f1eaf0ead05.aspx",  # 山家
        "https://www.gushiwen.cn/shiwenv_87a776797882.aspx",  # 小桃红·满城烟水月微茫
        "https://www.gushiwen.cn/shiwenv_46c8a2b5b4c5.aspx",  # 静夜思
    ]
    
    logger.info(f"开始批量测试 {len(test_urls)} 个页面")
    
    success_count = 0
    failed_count = 0
    results = []
    
    for i, url in enumerate(test_urls, 1):
        logger.info(f"[{i}/{len(test_urls)}] 正在处理: {url}")
        
        try:
            result = spider.crawl_poem(url)
            if result and result.get('title'):
                success_count += 1
                results.append(result)
                
                # 统计内容长度
                content_stats = {
                    'title': result.get('title', ''),
                    'author': result.get('author', ''),
                    'content_len': len(result.get('content', '')),
                    'translation_len': len(result.get('translation', '')),
                    'annotation_len': len(result.get('annotation', '')),
                    'appreciation_len': len(result.get('appreciation', '')),
                    'analysis_len': len(result.get('analysis', '')),
                    'background_len': len(result.get('background', ''))
                }
                
                logger.success(f"✓ 成功: {content_stats['title']} - {content_stats['author']}")
                logger.info(f"  内容长度: {content_stats['content_len']}")
                logger.info(f"  译文长度: {content_stats['translation_len']}")
                logger.info(f"  注释长度: {content_stats['annotation_len']}")
                logger.info(f"  赏析长度: {content_stats['appreciation_len']}")
                logger.info(f"  简析长度: {content_stats['analysis_len']}")
                logger.info(f"  背景长度: {content_stats['background_len']}")
                
            else:
                failed_count += 1
                logger.error(f"✗ 失败: {url}")
        except Exception as e:
            failed_count += 1
            logger.error(f"✗ 异常: {url} - {e}")
        
        # 添加延迟避免请求过快
        if i < len(test_urls):
            time.sleep(2)
    
    # 输出统计结果
    logger.info(f"\n=== 爬取完成 ===")
    logger.info(f"总计: {len(test_urls)} 个页面")
    logger.info(f"成功: {success_count} 个")
    logger.info(f"失败: {failed_count} 个")
    logger.info(f"成功率: {success_count/len(test_urls)*100:.1f}%")
    
    # 输出详细统计
    if results:
        logger.info(f"\n=== 内容统计 ===")
        total_translation = sum(len(r.get('translation', '')) for r in results)
        total_annotation = sum(len(r.get('annotation', '')) for r in results)
        total_appreciation = sum(len(r.get('appreciation', '')) for r in results)
        
        logger.info(f"平均译文长度: {total_translation/len(results):.0f} 字符")
        logger.info(f"平均注释长度: {total_annotation/len(results):.0f} 字符")
        logger.info(f"平均赏析长度: {total_appreciation/len(results):.0f} 字符")
        
        # 检查是否有空内容
        empty_translation = sum(1 for r in results if not r.get('translation'))
        empty_annotation = sum(1 for r in results if not r.get('annotation'))
        empty_appreciation = sum(1 for r in results if not r.get('appreciation'))
        
        logger.info(f"缺少译文的诗词: {empty_translation} 首")
        logger.info(f"缺少注释的诗词: {empty_annotation} 首")
        logger.info(f"缺少赏析的诗词: {empty_appreciation} 首")

if __name__ == "__main__":
    main()
