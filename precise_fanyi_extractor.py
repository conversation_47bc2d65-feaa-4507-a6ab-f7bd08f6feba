"""
精确提取古诗文网译文和注释的工具
专门针对 https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx 页面
"""
import requests
import re
from bs4 import BeautifulSoup
from loguru import logger

class PreciseFanyiExtractor:
    """精确的译文注释提取器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.setup_session()
        
    def setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)
    
    def extract_fanyi_content(self, url):
        """提取译文和注释内容"""
        try:
            response = self.session.get(url)
            response.raise_for_status()
            response.encoding = 'utf-8'
            logger.info(f"成功获取页面: {url}")
            
            soup = BeautifulSoup(response.text, 'lxml')
            
            # 查找fanyi div
            fanyi_divs = soup.find_all('div', id=re.compile(r'fanyi\d+'))
            logger.info(f"找到 {len(fanyi_divs)} 个fanyi div")
            
            results = {}
            
            for fanyi_div in fanyi_divs:
                div_id = fanyi_div.get('id')
                logger.info(f"处理 {div_id}")
                
                # 获取完整的HTML内容（保留标签）
                full_html = str(fanyi_div)
                
                # 获取纯文本内容
                full_text = fanyi_div.get_text()
                
                # 分析内容结构
                logger.info(f"HTML长度: {len(full_html)}")
                logger.info(f"文本长度: {len(full_text)}")
                logger.info(f"文本预览: {full_text[:100]}...")
                
                # 查找译文和注释的分界点
                translation_html = ""
                annotation_html = ""
                translation_text = ""
                annotation_text = ""
                
                # 方法1: 通过<p>标签分离
                paragraphs = fanyi_div.find_all('p')
                logger.info(f"找到 {len(paragraphs)} 个段落")
                
                for i, p in enumerate(paragraphs):
                    p_text = p.get_text().strip()
                    p_html = str(p)
                    
                    logger.info(f"段落 {i+1}: {p_text[:50]}...")
                    
                    if p_text.startswith('译文'):
                        # 这是译文段落
                        translation_text = p_text
                        translation_html = p_html
                        logger.info(f"找到译文段落，长度: {len(p_text)}")
                    elif p_text.startswith('注释'):
                        # 这是注释段落
                        annotation_text = p_text
                        annotation_html = p_html
                        logger.info(f"找到注释段落，长度: {len(p_text)}")
                
                # 方法2: 如果没有找到分离的段落，尝试从完整文本中分离
                if not translation_text and not annotation_text:
                    if '译文' in full_text and '注释' in full_text:
                        # 找到译文和注释的位置
                        translation_start = full_text.find('译文')
                        annotation_start = full_text.find('注释')
                        
                        if translation_start < annotation_start:
                            translation_text = full_text[translation_start:annotation_start].strip()
                            annotation_text = full_text[annotation_start:].strip()
                        else:
                            annotation_text = full_text[annotation_start:translation_start].strip()
                            translation_text = full_text[translation_start:].strip()
                        
                        logger.info(f"从完整文本分离 - 译文长度: {len(translation_text)}, 注释长度: {len(annotation_text)}")
                
                results[div_id] = {
                    'full_html': full_html,
                    'full_text': full_text,
                    'translation_html': translation_html,
                    'translation_text': translation_text,
                    'annotation_html': annotation_html,
                    'annotation_text': annotation_text
                }
            
            return results
            
        except Exception as e:
            logger.error(f"提取失败: {e}")
            return {}
    
    def extract_shangxi_content(self, url):
        """提取赏析内容"""
        try:
            response = self.session.get(url)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            soup = BeautifulSoup(response.text, 'lxml')
            
            # 查找shangxi div
            shangxi_divs = soup.find_all('div', id=re.compile(r'shangxi\d+'))
            logger.info(f"找到 {len(shangxi_divs)} 个shangxi div")
            
            results = {}
            
            for shangxi_div in shangxi_divs:
                div_id = shangxi_div.get('id')
                logger.info(f"处理赏析 {div_id}")
                
                # 获取完整的HTML内容（保留标签）
                full_html = str(shangxi_div)
                
                # 获取纯文本内容
                full_text = shangxi_div.get_text().strip()
                
                logger.info(f"赏析HTML长度: {len(full_html)}")
                logger.info(f"赏析文本长度: {len(full_text)}")
                logger.info(f"赏析文本预览: {full_text[:100]}...")
                
                results[div_id] = {
                    'full_html': full_html,
                    'full_text': full_text
                }
            
            return results
            
        except Exception as e:
            logger.error(f"提取赏析失败: {e}")
            return {}

def main():
    """测试函数"""
    extractor = PreciseFanyiExtractor()
    
    test_url = "https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx"
    
    print("=== 提取译文和注释 ===")
    fanyi_results = extractor.extract_fanyi_content(test_url)
    
    for div_id, content in fanyi_results.items():
        print(f"\n【{div_id}】")
        print(f"完整文本长度: {len(content['full_text'])}")
        print(f"译文文本长度: {len(content['translation_text'])}")
        print(f"注释文本长度: {len(content['annotation_text'])}")
        
        if content['translation_text']:
            print(f"\n译文内容:\n{content['translation_text'][:200]}...")
        
        if content['annotation_text']:
            print(f"\n注释内容:\n{content['annotation_text'][:200]}...")
        
        if content['translation_html']:
            print(f"\n译文HTML:\n{content['translation_html'][:300]}...")
    
    print("\n" + "="*50)
    print("=== 提取赏析内容 ===")
    shangxi_results = extractor.extract_shangxi_content(test_url)
    
    for div_id, content in shangxi_results.items():
        print(f"\n【{div_id}】")
        print(f"赏析文本长度: {len(content['full_text'])}")
        print(f"赏析内容预览:\n{content['full_text'][:200]}...")

if __name__ == "__main__":
    main()
