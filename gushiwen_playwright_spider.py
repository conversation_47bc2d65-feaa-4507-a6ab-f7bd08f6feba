"""
使用Playwright的古诗文网详情页面爬虫
可以处理JavaScript动态加载的内容，包括点击"展开阅读全文"按钮
"""
import asyncio
import re
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
from loguru import logger
from models import SessionLocal, Poem
from config import SPIDER_CONFIG


class GuShiWenPlaywrightSpider:
    """使用Playwright的古诗文网详情页面爬虫"""

    def __init__(self):
        self.delay = SPIDER_CONFIG['request_delay']

    async def get_page_content(self, url):
        """使用Playwright获取页面内容并处理动态加载"""
        async with async_playwright() as p:
            # 启动浏览器，添加反检测参数
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080},
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                }
            )
            page = await context.new_page()

            # 隐藏webdriver特征
            await page.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)

            try:
                # 访问页面
                await page.goto(url, wait_until='networkidle')
                logger.info(f"成功访问页面: {url}")

                # 等待页面加载完成
                await page.wait_for_timeout(2000)

                # 查找并点击所有"展开阅读全文"按钮 - 尝试多种选择器
                expand_selectors = [
                    'a[onclick*="more"]',
                    'a[onclick*="More"]',
                    'a:has-text("展开阅读全文")',
                    'a:has-text("展开全文")',
                    'a:has-text("更多")',
                    '.more',
                    '[onclick*="more"]'
                ]

                total_buttons = 0
                for selector in expand_selectors:
                    try:
                        buttons = await page.query_selector_all(selector)
                        if buttons:
                            logger.info(
                                f"使用选择器 '{selector}' 找到 {len(buttons)} 个按钮")
                            for i, button in enumerate(buttons):
                                try:
                                    is_visible = await button.is_visible()
                                    if is_visible:
                                        await button.click()
                                        logger.info(
                                            f"点击了按钮 {total_buttons + i + 1}")
                                        await page.wait_for_timeout(1000)
                                except Exception as e:
                                    logger.warning(f"点击按钮失败: {e}")
                            total_buttons += len(buttons)
                    except Exception as e:
                        logger.debug(f"选择器 '{selector}' 查找失败: {e}")

                logger.info(f"总共找到并处理了 {total_buttons} 个展开按钮")

                # 再次等待确保所有内容都加载完成
                await page.wait_for_timeout(2000)

                # 获取完整的HTML内容
                html_content = await page.content()
                logger.info("成功获取完整页面内容")

                return html_content

            except Exception as e:
                logger.error(f"获取页面内容失败: {e}")
                return None
            finally:
                await browser.close()

    def parse_poem_detail(self, html_content, url):
        """解析诗词详情页面"""
        if not html_content:
            return None

        soup = BeautifulSoup(html_content, 'lxml')

        # 提取基本信息
        poem_data = {
            'source_url': url,
            'title': '',
            'author': '',
            'dynasty': '',
            'content': '',
            'translation': '',
            'annotation': '',
            'appreciation': '',
            'background': '',
            'analysis': ''
        }

        try:
            # 1. 提取诗词标题 - 只取第一个（主要诗词的标题）
            title_elems = soup.find_all('h1')
            if title_elems:
                poem_data['title'] = title_elems[0].get_text().strip()
                logger.info(f"提取标题: {poem_data['title']}")

            # 2. 提取作者和朝代信息 - 只取第一个（主要诗词的信息）
            author_elems = soup.find_all('p', class_='source')
            if author_elems:
                author_elem = author_elems[0]  # 只取第一个
                # 提取作者名 - 第一个链接通常是作者
                author_links = author_elem.find_all('a')
                if author_links:
                    # 第一个链接是作者
                    author_text = author_links[0].get_text().strip()
                    # 移除可能的重复文本
                    if ' ' in author_text:
                        poem_data['author'] = author_text.split()[0]
                    else:
                        poem_data['author'] = author_text

                    # 第二个链接通常是朝代
                    if len(author_links) > 1:
                        dynasty_text = author_links[1].get_text().strip()
                        poem_data['dynasty'] = dynasty_text.replace(
                            '〔', '').replace('〕', '')

                logger.info(
                    f"提取作者: {poem_data['author']}, 朝代: {poem_data['dynasty']}")

            # 3. 提取诗词内容 - 只取第一个（主要诗词的内容）
            content_divs = soup.find_all('div', class_='contson')
            if content_divs:
                content_div = content_divs[0]  # 只取第一个
                # 获取文本内容，保持原有的换行格式
                content_html = str(content_div)
                content_html = content_html.replace(
                    '<br>', '\n').replace('<br/>', '\n')
                content_soup = BeautifulSoup(content_html, 'lxml')
                poem_data['content'] = content_soup.get_text().strip()
                logger.info(f"提取内容长度: {len(poem_data['content'])}")

            # 4. 提取译文及注释 - 现在应该能获取完整内容
            fanyi_divs = soup.find_all('div', id=re.compile(r'fanyi\d+'))
            if fanyi_divs:
                translation_parts = []
                annotation_parts = []

                for fanyi_div in fanyi_divs:
                    # 获取所有文本内容
                    full_text = fanyi_div.get_text()

                    # 分割译文和注释
                    if '译文' in full_text and '注释' in full_text:
                        # 找到译文和注释的位置
                        translation_start = full_text.find('译文')
                        annotation_start = full_text.find('注释')

                        if translation_start < annotation_start:
                            # 译文在前，注释在后
                            translation_text = full_text[translation_start +
                                                         2:annotation_start].strip()
                            annotation_text = full_text[annotation_start + 2:].strip()
                        else:
                            # 注释在前，译文在后
                            annotation_text = full_text[annotation_start +
                                                        2:translation_start].strip()
                            translation_text = full_text[translation_start + 2:].strip()

                        if translation_text:
                            translation_parts.append(translation_text)
                        if annotation_text:
                            annotation_parts.append(annotation_text)

                    elif '译文' in full_text:
                        translation_start = full_text.find('译文')
                        translation_text = full_text[translation_start + 2:].strip()
                        if translation_text:
                            translation_parts.append(translation_text)

                    elif '注释' in full_text:
                        annotation_start = full_text.find('注释')
                        annotation_text = full_text[annotation_start + 2:].strip()
                        if annotation_text:
                            annotation_parts.append(annotation_text)

                poem_data['translation'] = '\n'.join(translation_parts)
                poem_data['annotation'] = '\n'.join(annotation_parts)
                logger.info(f"提取译文长度: {len(poem_data['translation'])}")
                logger.info(f"提取注释长度: {len(poem_data['annotation'])}")

            # 5. 提取赏析、简析、创作背景 - 现在应该能获取完整内容
            shangxi_divs = soup.find_all('div', id=re.compile(r'shangxi\d+'))
            appreciation_parts = []
            analysis_parts = []
            background_parts = []

            for shangxi_div in shangxi_divs:
                # 查找标题来确定内容类型
                h2_elem = shangxi_div.find('h2')
                if h2_elem:
                    section_title = h2_elem.get_text().strip()
                    # 获取完整文本内容
                    full_text = shangxi_div.get_text().strip()
                    # 移除标题部分
                    content_text = full_text.replace(section_title, '').strip()

                    if content_text:
                        if '赏析' in section_title:
                            appreciation_parts.append(content_text)
                        elif '简析' in section_title:
                            analysis_parts.append(content_text)
                        elif '创作背景' in section_title or '背景' in section_title:
                            background_parts.append(content_text)

            # 6. 查找直接的简析和创作背景部分
            sons_divs = soup.find_all('div', class_='sons')
            for section in sons_divs:
                h2_elem = section.find('h2')
                if h2_elem:
                    section_title = h2_elem.get_text().strip()
                    # 获取完整文本内容
                    full_text = section.get_text().strip()
                    # 移除标题部分
                    content_text = full_text.replace(section_title, '').strip()

                    if content_text:
                        if '简析' in section_title:
                            analysis_parts.append(content_text)
                        elif '创作背景' in section_title or '背景' in section_title:
                            background_parts.append(content_text)

            poem_data['appreciation'] = '\n'.join(appreciation_parts)
            poem_data['analysis'] = '\n'.join(analysis_parts)
            poem_data['background'] = '\n'.join(background_parts)

            logger.info(f"提取赏析长度: {len(poem_data['appreciation'])}")
            logger.info(f"提取简析长度: {len(poem_data['analysis'])}")
            logger.info(f"提取创作背景长度: {len(poem_data['background'])}")

            return poem_data

        except Exception as e:
            logger.error(f"解析页面内容时出错: {e}")
            return None

    def save_to_db(self, poem_data):
        """保存诗词数据到数据库"""
        if not poem_data or not poem_data.get('title'):
            logger.warning("诗词数据为空或缺少标题，跳过保存")
            return False

        db = SessionLocal()
        try:
            # 检查是否已存在
            existing = db.query(Poem).filter(
                Poem.title == poem_data['title'],
                Poem.author == poem_data['author']
            ).first()

            if existing:
                # 更新现有记录
                existing.translation = poem_data['translation']
                existing.annotation = poem_data['annotation']
                existing.appreciation = poem_data['appreciation']
                db.commit()
                logger.info(
                    f"更新现有诗词: {poem_data['title']} - {poem_data['author']}")
                return True
            else:
                # 创建新记录
                poem = Poem(
                    title=poem_data['title'],
                    author=poem_data['author'],
                    dynasty=poem_data['dynasty'],
                    content=poem_data['content'],
                    translation=poem_data['translation'],
                    annotation=poem_data['annotation'],
                    appreciation=poem_data['appreciation'],
                    source_url=poem_data['source_url']
                )

                db.add(poem)
                db.commit()
                logger.success(
                    f"成功保存新诗词: {poem_data['title']} - {poem_data['author']}")
                return True

        except Exception as e:
            db.rollback()
            logger.error(f"保存诗词到数据库失败: {e}")
            return False
        finally:
            db.close()

    async def crawl_poem(self, url):
        """爬取单个诗词页面"""
        logger.info(f"开始爬取诗词: {url}")

        # 获取页面内容
        html_content = await self.get_page_content(url)
        if not html_content:
            logger.error(f"获取页面内容失败: {url}")
            return None

        # 解析内容
        poem_data = self.parse_poem_detail(html_content, url)
        if poem_data:
            success = self.save_to_db(poem_data)
            if success:
                logger.success(f"成功爬取并保存: {poem_data['title']}")
            return poem_data
        else:
            logger.error(f"解析页面失败: {url}")
            return None


async def main():
    """测试函数"""
    spider = GuShiWenPlaywrightSpider()

    # 测试URL
    test_url = "https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx"

    result = await spider.crawl_poem(test_url)
    if result:
        print("\n=== 爬取结果 ===")
        for key, value in result.items():
            if value:
                print(f"{key}: {value[:100]}..." if len(
                    str(value)) > 100 else f"{key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())
