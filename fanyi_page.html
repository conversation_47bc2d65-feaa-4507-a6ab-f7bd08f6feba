
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" id="html">
<head><meta http-equiv="Cache-Control" content="no-siteapp" /><meta http-equiv="Cache-Control" content="no-transform " /><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><title>
</title>
<meta name="description" content="" />
<script type="text/javascript">if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {window.location.href ="https://m.gushiwen.cn/shiwenv_fanyi_56757.aspx";} else {}
</script>
<link href="/css/skin.css?time=250625" rel="stylesheet" type="text/css" /><link href="/css/layui.css?time=250625" rel="stylesheet" type="text/css" /><link href="/css/play.css?time=250625" rel="stylesheet" type="text/css" />
<script src="/js/jquery-3.2.1.min.js" type="text/javascript"></script>
<script type="text/javascript">function getCookie(name) {var arr,reg =new RegExp("(^| )" + name + "=([^;]*)(;|$)");if (arr =document.cookie.match(reg))
return unescape(arr[2]);else
return null;}
 function selectLike(id) {document.getElementById('likeImg' + id).name =parseInt(document.getElementById('likeImg' + id).name) + 1;if (document.getElementById('likeImg' + id).name =='1') {var idsShigeLaiyo =getCookie('idsShiwen2017');if (idsShigeLaiyo !=null &&idsShigeLaiyo !='') {var ids =idsShigeLaiyo.split(',');for (var i =0;i < ids.length;i++) {if (ids[i] ==id) {document.getElementById('likeImg' + id).src ='https://ziyuan.guwendao.net/siteimg/shou-cangok.png';document.getElementById('likeImg' + id).alt ='已收藏';break;}
}
}
}
}
 function selectLikeMingju(id) {document.getElementById('likeImg' + id).name =parseInt(document.getElementById('likeImg' + id).name) + 1;if (document.getElementById('likeImg' + id).name =='1') {var idsShigeLaiyo =getCookie('idsMingju2017');if (idsShigeLaiyo !=null &&idsShigeLaiyo !='') {var ids =idsShigeLaiyo.split(',');for (var i =0;i < ids.length;i++) {if (ids[i] ==id) {document.getElementById('likeImg' + id).src ='https://ziyuan.guwendao.net/siteimg/shou-cangok.png';document.getElementById('likeImg' + id).alt ='已收藏';break;}
}
}
}
}
 function selectLikeAuthor(id) {document.getElementById('likeImg' + id).name =parseInt(document.getElementById('likeImg' + id).name) + 1;if (document.getElementById('likeImg' + id).name =='1') {var idsShigeLaiyo =getCookie('idsAuthor2017');if (idsShigeLaiyo !=null &&idsShigeLaiyo !='') {var ids =idsShigeLaiyo.split(',');for (var i =0;i < ids.length;i++) {if (ids[i] ==id) {document.getElementById('likeImg' + id).src ='https://ziyuan.guwendao.net/siteimg/shou-cangok.png';document.getElementById('likeImg' + id).alt ='已收藏';break;}
}
}
}
}
 function selectLikeGuwen(id) {document.getElementById('likeImg' + id).name =parseInt(document.getElementById('likeImg' + id).name) + 1;if (document.getElementById('likeImg' + id).name =='1') {var idsShigeLaiyo =getCookie('idsGuji2017');if (idsShigeLaiyo !=null &&idsShigeLaiyo !='') {var ids =idsShigeLaiyo.split(',');for (var i =0;i < ids.length;i++) {if (ids[i] ==id) {document.getElementById('likeImg' + id).src ='https://ziyuan.guwendao.net/siteimg/shou-cangok.png';document.getElementById('likeImg' + id).alt ='已收藏';break;}
}
}
}
}
</script>
<script>var _hmt =_hmt ||[];(function () {var hm =document.createElement("script");hm.src ="//hm.baidu.com/hm.js?9007fab6814e892d3020a64454da5a55";var s =document.getElementsByTagName("script")[0];s.parentNode.insertBefore(hm,s);})();</script>
</head>
<body onclick="closeshowBos()">
<div class="maintopbc" style=" height:45px; background:url(https://ziyuan.guwendao.net/siteimg/24jie/%e5%a4%a7%e6%9a%91small.jpg) top center no-repeat; background-size:cover;">
<div class="maintop" style="opacity:0.94;">
<div class="cont">
<div class="left">
<a href="/">古诗文网</a>
</div>
<div class="right">
<div class="son1">
<a style="margin-left:1px;" href="/">推荐</a>
<a href="/shiwens/" style="border-bottom:3px solid #5D6146; font-weight:bold; line-height:45px; height:43px;">诗文</a>
<a href="/mingjus/">名句</a>
<a href="/authors/">作者</a>
<a href="/guwen/">古籍</a>
<a href="/user/collect.aspx" rel="nofollow">我的</a>
<a style="width:65px;" href="/app/" target="_blank">APP</a>
</div>
<div class="son2">
<div class="search">
<form action="/search.aspx" onsubmit="return selectSearch()" contentType="text/html; charset=utf-8">
<input onkeydown="noajaxkeyUp()" oninput="goshowBos()" id="txtKey" name="value" type="text" value="" maxlength="40" autocomplete="off" style="height:25px; line-height:25px; float:left; padding-left:10px; width:255px; font-size:14px; clear:left; border:0px;" />
<input type="submit" style="float:right; width:23px; height:23px; clear:right; margin-top:2px; margin-right:4px; background-image:url(https://ziyuan.guwendao.net/siteimg/docSearch230511.png); background-repeat:no-repeat; background-size:23px 23px; border:0px;cursor:pointer;" value="" />
<input id="b" style="display:none;" type="text" />
</form>
</div>
</div>
</div>
</div>
</div>
<div class="main3">
<div style="width:300px; float:right;">
<div id="box"></div>
</div>
</div>
</div>
<div class="container" id="container">
<div class="audio-player-container" id="audioplayercontainer">
<div class="audio-player-controls">
<button id="prevButton"><img src="/img/play/prevButton.png" /></button>
<button id="playPauseButton"><img src="/img/play/playPauseButton.png" /></button>
<button id="nextButton"><img src="/img/play/nextButton.png" /></button>
<div class="progress-container">
<div class="time-info">
<div class="time-infoleft">
<span class="timenamestr" id="nameStr">东北一枝花</span>
<span class="time-langsong" id="author">-张哈哈</span>
</div>
<div class="time-inforhgit">
<span class="time-start" id="currentTime">0:00</span> /
<span class="time-end" id="duration">0:00</span>
<span class="time-langsong" id="langsongspan">(朗诵：<span id="langsongauthor">琼花</span>)</span>
</div>
</div>
<div class="progressBackground" id="progressBackground"></div>
<div class="progress" id="progress"></div>
<div class="progressBall" id="progressBall"></div>
</div>
<div class="close-button" id="closeButton"><img src="/img/play/close.png" /></div>
<button id="xunhuanButton"><img src="/img/play/listplay.png" id="currentModeIcon"/></button>
<button id="beisuButton"><img src="/img/play/beisu.png" /></button>
<div class="listButton">
<span class="payaaa">
<button id="listButton">
<img src="/img/play/list.png" alt="播放列表按钮" />
</button>
<span id="palynum">12</span>
</span>
<div class="playlist-container" id="playlistcontainer">
<div class="playlist-header">
播放列表 <span class="right-icons">
<img src="/img/play/clear.png" class="icon-space" id="clear" />
<img src="/img/play/playclose.png" class="icon-space" id="playclose"/>
</span>
</div>
<div class="playlist-wrapper" id="playlistWrapper">
<ul class="playlist" id="playlist">初始的播放列表项</ul>
</div>
</div>
</div>
<div class="volume-control">
<span id="volume"><img src="/img/play/volume.png" /></span>
<input type="range" id="volumeControl" min="0" max="1" step="0.01" value="0.5">
</div>
<div class="speed-controls" id="speedControls">
<ul class="speed-options" id="speedOptions">
<li data-value="0.25" class="list-item">0.25x</li>
<li data-value="0.5" class="list-item">0.5x</li>
<li data-value="0.75" class="list-item">0.75x</li>
<li data-value="1" class="list-item selected">1.0x</li>
<li data-value="1.25" class="list-item">1.25x</li>
<li data-value="1.5" class="list-item">1.5x</li>
<li data-value="2" class="list-item">2.0x</li>
</ul>
</div>
<div class="play-mode" id="playModeControls">
<ul class="play-options" id="playModeOptions">
<li data-value="listLoop" class="list-item selected"><img src="/img/play/listplay.png" class="icon" />列表循环</li>
<li data-value="random" class="list-item"><img src="/img/play/random.png" class="icon" />随机播放</li>
<li data-value="singleLoop" class="list-item"><img src="/img/play/singpaly.png" class="icon" />单曲循环</li>
<li data-value="single" class="list-item"><img src="/img/play/sing.png" class="icon" />单曲播放</li>
</ul>
</div>
</div>
<audio id="audioPlayer" style="display: none;">您的浏览器不支持<code>audio</code> 元素。</audio>
</div>
</div>
<div class="main3" style="font-size: 20px; text-align: center; padding-top: 200px; margin-bottom: 200px;">
暂无内容
</div>
<div class="main4">
© 2025 <a href="/">古诗文网</a> | <a href="/shiwens/">诗文</a> | <a href="/mingjus/">名句</a> | <a href="/authors/">作者</a> | <a href="/guwen/">古籍</a> | <a href="/jiucuo.aspx?u=" target="_blank" rel="nofollow">纠错</a>
</div>
<script type="text/javascript">window.onload =function () {setIframeHeight(document.getElementById('external-frame'));};if (getCookie('gsw2017user') !=null &&getCookie('gsw2017user').split('|').length >=3) {var userDate =getCookie('gsw2017user').split('|')[2];var myDate =new Date(userDate);var now =new Date();if (myDate >=now) {for (var i =0;i < document.getElementsByClassName("abcd").length;i++) {document.getElementsByClassName("abcd")[i].style.display ='none';}
}
if (getCookie('gsw2017user').split('|').length >=4) {userDate =getCookie('gsw2017user').split('|')[3];myDate =new Date(userDate);if (myDate >=now) {for (var i =0;i < document.getElementsByClassName("abcd").length;i++) {document.getElementsByClassName("abcd")[i].style.display ='none';}
}
}
}
</script>
<script defer="defer" src="/js/play.js?time=250625" type="text/javascript"></script>
<script defer="defer" src="/js/layui.js?time=250625" type="text/javascript"></script>
<script defer="defer" src="/js/skin.js?time=250625" type="text/javascript"></script>
<script defer="defer" src="/js/listenerPlay.js?time=250625" type="text/javascript"></script>
</body>
</html>
