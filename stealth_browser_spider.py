"""
隐蔽的浏览器自动化爬虫
使用更多反检测技术来模拟真实用户操作
"""
import asyncio
import time
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
from loguru import logger


class StealthBrowserSpider:
    """隐蔽的浏览器爬虫"""

    def __init__(self):
        self.delay = 2

    async def create_stealth_browser(self):
        """创建隐蔽的浏览器实例"""
        playwright = await async_playwright().start()

        # 使用更多反检测参数
        browser = await playwright.chromium.launch(
            headless=True,  # 改为headless模式
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-blink-features=AutomationControlled',
                '--disable-background-networking',
                '--disable-background-timer-throttling',
                '--disable-renderer-backgrounding',
                '--disable-backgrounding-occluded-windows',
                '--disable-restore-session-state',
                '--disable-ipc-flooding-protection',
                '--disable-hang-monitor',
                '--disable-popup-blocking',
                '--disable-prompt-on-repost',
                '--disable-sync',
                '--force-color-profile=srgb',
                '--metrics-recording-only',
                '--safebrowsing-disable-auto-update',
                '--enable-automation',
                '--password-store=basic',
                '--use-mock-keychain'
            ]
        )

        # 创建上下文，模拟真实用户环境
        context = await browser.new_context(
            viewport={'width': 1366, 'height': 768},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='zh-CN',
            timezone_id='Asia/Shanghai',
            permissions=['geolocation'],
            extra_http_headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1'
            }
        )

        page = await context.new_page()

        # 注入反检测脚本
        await page.add_init_script("""
            // 移除webdriver痕迹
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 伪造chrome对象
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };
            
            // 伪造插件
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // 伪造语言
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            // 移除自动化标识
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        """)

        return playwright, browser, context, page

    async def simulate_human_behavior(self, page):
        """模拟人类行为"""
        # 随机移动鼠标
        await page.mouse.move(100, 100)
        await asyncio.sleep(0.5)
        await page.mouse.move(200, 200)
        await asyncio.sleep(0.3)

        # 模拟滚动
        await page.evaluate("window.scrollTo(0, 100)")
        await asyncio.sleep(0.5)
        await page.evaluate("window.scrollTo(0, 0)")
        await asyncio.sleep(0.3)

    async def extract_full_content_with_click(self, url):
        """通过点击展开按钮获取完整内容"""
        playwright, browser, context, page = await self.create_stealth_browser()

        try:
            logger.info(f"开始访问页面: {url}")

            # 访问页面
            await page.goto(url, wait_until='networkidle', timeout=30000)
            logger.info("页面加载完成")

            # 等待页面完全加载
            await asyncio.sleep(3)

            # 模拟人类行为
            await self.simulate_human_behavior(page)

            # 检查页面是否正确加载
            title = await page.title()
            logger.info(f"页面标题: {title}")

            # 检查是否被重定向到登录页面
            current_url = page.url
            if 'login' in current_url.lower() or 'denglu' in current_url.lower():
                logger.error("页面被重定向到登录页面")
                return None

            # 保存初始页面内容
            initial_content = await page.content()
            logger.info(f"初始页面内容长度: {len(initial_content)}")

            # 查找展开按钮
            expand_selectors = [
                'a[href*="fanyiShow"]',
                'a[onclick*="fanyiShow"]',
                'a:has-text("展开阅读全文")',
                'a:has-text("展开全文")',
                'div[onclick*="fanyiShow"]',
                '[onclick*="fanyiShow"]'
            ]

            clicked_buttons = 0

            for selector in expand_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    logger.info(f"选择器 '{selector}' 找到 {len(elements)} 个元素")

                    for i, element in enumerate(elements):
                        try:
                            # 检查元素是否可见
                            is_visible = await element.is_visible()
                            if not is_visible:
                                continue

                            # 滚动到元素位置
                            await element.scroll_into_view_if_needed()
                            await asyncio.sleep(0.5)

                            # 模拟鼠标悬停
                            await element.hover()
                            await asyncio.sleep(0.3)

                            # 点击元素
                            await element.click()
                            clicked_buttons += 1
                            logger.info(f"成功点击第 {clicked_buttons} 个展开按钮")

                            # 等待内容加载
                            await asyncio.sleep(2)

                        except Exception as e:
                            logger.debug(f"点击元素失败: {e}")

                except Exception as e:
                    logger.debug(f"选择器 '{selector}' 查找失败: {e}")

            logger.info(f"总共点击了 {clicked_buttons} 个展开按钮")

            # 等待所有内容加载完成
            await asyncio.sleep(3)

            # 获取最终页面内容
            final_content = await page.content()
            logger.info(f"最终页面内容长度: {len(final_content)}")

            # 保存最终页面用于调试
            with open('final_page_content.html', 'w', encoding='utf-8') as f:
                f.write(final_content)

            return final_content

        except Exception as e:
            logger.error(f"浏览器操作失败: {e}")
            return None
        finally:
            await browser.close()
            await playwright.stop()

    def parse_full_content(self, html_content, url):
        """解析完整的页面内容"""
        if not html_content:
            return None

        soup = BeautifulSoup(html_content, 'lxml')

        result = {
            'source_url': url,
            'title': '',
            'author': '',
            'dynasty': '',
            'content': '',
            'translation': '',
            'translation_html': '',
            'annotation': '',
            'annotation_html': '',
            'appreciation': '',
            'appreciation_html': ''
        }

        try:
            # 提取标题
            title_elems = soup.find_all('h1')
            if title_elems:
                result['title'] = title_elems[0].get_text().strip()
                logger.info(f"提取标题: {result['title']}")

            # 提取作者和朝代
            author_elems = soup.find_all('p', class_='source')
            if author_elems:
                author_elem = author_elems[0]
                author_links = author_elem.find_all('a')
                if author_links:
                    result['author'] = author_links[0].get_text().strip().split()[
                        0]
                    if len(author_links) > 1:
                        dynasty_text = author_links[1].get_text().strip()
                        result['dynasty'] = dynasty_text.replace(
                            '〔', '').replace('〕', '')

            # 提取诗词内容
            content_divs = soup.find_all('div', class_='contson')
            if content_divs:
                content_div = content_divs[0]
                content_html = str(content_div)
                content_html = content_html.replace(
                    '<br>', '\n').replace('<br/>', '\n')
                content_soup = BeautifulSoup(content_html, 'lxml')
                result['content'] = content_soup.get_text().strip()

            # 提取译文和注释（现在应该是完整的）
            fanyi_divs = soup.find_all('div', id=lambda x: x and 'fanyi' in x)
            for fanyi_div in fanyi_divs:
                paragraphs = fanyi_div.find_all('p')

                for p in paragraphs:
                    text = p.get_text().strip()
                    html = str(p)

                    if text.startswith('译文') and len(text) > len(result['translation']):
                        result['translation'] = text
                        result['translation_html'] = html
                        logger.info(f"提取完整译文长度: {len(text)}")

                    elif text.startswith('注释') and len(text) > len(result['annotation']):
                        result['annotation'] = text
                        result['annotation_html'] = html
                        logger.info(f"提取完整注释长度: {len(text)}")

            # 提取赏析
            shangxi_divs = soup.find_all(
                'div', id=lambda x: x and 'shangxi' in x)
            for shangxi_div in shangxi_divs:
                h2_elem = shangxi_div.find('h2')
                if h2_elem and '赏析' in h2_elem.get_text():
                    result['appreciation'] = shangxi_div.get_text().strip()
                    result['appreciation_html'] = str(shangxi_div)
                    logger.info(f"提取赏析长度: {len(result['appreciation'])}")
                    break

            return result

        except Exception as e:
            logger.error(f"解析内容失败: {e}")
            return None

    async def crawl_with_full_content(self, url):
        """爬取完整内容"""
        logger.info(f"开始使用浏览器自动化爬取: {url}")

        # 获取完整页面内容
        html_content = await self.extract_full_content_with_click(url)
        if not html_content:
            logger.error("获取页面内容失败")
            return None

        # 解析内容
        result = self.parse_full_content(html_content, url)
        if result:
            logger.success(f"成功提取完整内容: {result['title']}")
            return result
        else:
            logger.error("解析内容失败")
            return None


async def main():
    """测试函数"""
    spider = StealthBrowserSpider()

    test_url = "https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx"

    result = await spider.crawl_with_full_content(test_url)

    if result:
        print("\n=== 浏览器自动化爬取结果 ===")
        print(f"标题: {result['title']}")
        print(f"作者: {result['author']} ({result['dynasty']})")
        print(f"译文长度: {len(result['translation'])}")
        print(f"注释长度: {len(result['annotation'])}")
        print(f"赏析长度: {len(result['appreciation'])}")

        if result['annotation']:
            print(f"\n完整注释内容:\n{result['annotation'][:300]}...")

        if result['annotation_html']:
            print(f"\n注释HTML:\n{result['annotation_html'][:300]}...")

if __name__ == "__main__":
    asyncio.run(main())
