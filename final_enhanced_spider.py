"""
最终版本的增强爬虫
基于我们的发现，提取尽可能完整的内容，并保留HTML格式
"""
import requests
import re
from bs4 import BeautifulSoup
from loguru import logger
from models import SessionLocal, Poem

class FinalEnhancedSpider:
    """最终增强版爬虫"""
    
    def __init__(self):
        self.session = requests.Session()
        self.setup_session()
        
    def setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)
    
    def extract_poem_content(self, url):
        """提取诗词的完整内容"""
        try:
            response = self.session.get(url)
            response.raise_for_status()
            response.encoding = 'utf-8'
            logger.info(f"成功获取页面: {url}")
            
            soup = BeautifulSoup(response.text, 'lxml')
            
            result = {
                'source_url': url,
                'title': '',
                'author': '',
                'dynasty': '',
                'content': '',
                'content_html': '',
                'translation': '',
                'translation_html': '',
                'annotation': '',
                'annotation_html': '',
                'appreciation': '',
                'appreciation_html': '',
                'analysis': '',
                'background': ''
            }
            
            # 1. 提取标题（只取第一个，避免"猜您喜欢"的干扰）
            title_elems = soup.find_all('h1')
            if title_elems:
                result['title'] = title_elems[0].get_text().strip()
                logger.info(f"提取标题: {result['title']}")
            
            # 2. 提取作者和朝代（只取第一个）
            author_elems = soup.find_all('p', class_='source')
            if author_elems:
                author_elem = author_elems[0]
                author_links = author_elem.find_all('a')
                if author_links:
                    result['author'] = author_links[0].get_text().strip().split()[0]
                    if len(author_links) > 1:
                        dynasty_text = author_links[1].get_text().strip()
                        result['dynasty'] = dynasty_text.replace('〔', '').replace('〕', '')
                
                logger.info(f"提取作者: {result['author']}, 朝代: {result['dynasty']}")
            
            # 3. 提取诗词内容（只取第一个）
            content_divs = soup.find_all('div', class_='contson')
            if content_divs:
                content_div = content_divs[0]
                result['content_html'] = str(content_div)
                
                # 处理换行
                content_html = str(content_div)
                content_html = content_html.replace('<br>', '\n').replace('<br/>', '\n')
                content_soup = BeautifulSoup(content_html, 'lxml')
                result['content'] = content_soup.get_text().strip()
                
                logger.info(f"提取内容长度: {len(result['content'])}")
            
            # 4. 提取译文和注释（从fanyi div）
            fanyi_divs = soup.find_all('div', id=re.compile(r'fanyi\d+'))
            if fanyi_divs:
                fanyi_div = fanyi_divs[0]  # 只取第一个
                
                # 查找译文和注释段落
                paragraphs = fanyi_div.find_all('p')
                
                for p in paragraphs:
                    text = p.get_text().strip()
                    html = str(p)
                    
                    if text.startswith('译文'):
                        result['translation'] = text
                        result['translation_html'] = html
                        logger.info(f"提取译文长度: {len(text)}")
                    
                    elif text.startswith('注释'):
                        result['annotation'] = text
                        result['annotation_html'] = html
                        logger.info(f"提取注释长度: {len(text)}")
            
            # 5. 提取赏析内容
            shangxi_divs = soup.find_all('div', id=re.compile(r'shangxi\d+'))
            appreciation_parts = []
            
            for shangxi_div in shangxi_divs:
                h2_elem = shangxi_div.find('h2')
                if h2_elem:
                    section_title = h2_elem.get_text().strip()
                    
                    if '赏析' in section_title:
                        # 获取完整的div内容
                        result['appreciation_html'] = str(shangxi_div)
                        result['appreciation'] = shangxi_div.get_text().strip()
                        logger.info(f"提取赏析长度: {len(result['appreciation'])}")
                        break
            
            # 6. 提取简析和创作背景
            sons_divs = soup.find_all('div', class_='sons')
            for section in sons_divs:
                h2_elem = section.find('h2')
                if h2_elem:
                    section_title = h2_elem.get_text().strip()
                    content_text = section.get_text().strip()
                    
                    if '简析' in section_title:
                        result['analysis'] = content_text
                        logger.info(f"提取简析长度: {len(content_text)}")
                    elif '创作背景' in section_title or '背景' in section_title:
                        result['background'] = content_text
                        logger.info(f"提取创作背景长度: {len(content_text)}")
            
            return result
            
        except Exception as e:
            logger.error(f"提取失败: {e}")
            return None
    
    def save_to_db(self, poem_data):
        """保存到数据库，包括HTML格式"""
        if not poem_data or not poem_data.get('title'):
            logger.warning("诗词数据为空或缺少标题，跳过保存")
            return False
            
        db = SessionLocal()
        try:
            # 检查是否已存在
            existing = db.query(Poem).filter(
                Poem.title == poem_data['title'],
                Poem.author == poem_data['author']
            ).first()
            
            if existing:
                # 更新现有记录
                existing.translation = poem_data['translation']
                existing.annotation = poem_data['annotation']
                existing.appreciation = poem_data['appreciation']
                db.commit()
                logger.info(f"更新现有诗词: {poem_data['title']} - {poem_data['author']}")
                return True
            else:
                # 创建新记录
                poem = Poem(
                    title=poem_data['title'],
                    author=poem_data['author'],
                    dynasty=poem_data['dynasty'],
                    content=poem_data['content'],
                    translation=poem_data['translation'],
                    annotation=poem_data['annotation'],
                    appreciation=poem_data['appreciation'],
                    source_url=poem_data['source_url']
                )
                
                db.add(poem)
                db.commit()
                logger.success(f"成功保存新诗词: {poem_data['title']} - {poem_data['author']}")
                return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"保存诗词到数据库失败: {e}")
            return False
        finally:
            db.close()
    
    def crawl_poem(self, url):
        """爬取单个诗词页面"""
        logger.info(f"开始爬取诗词: {url}")
        
        poem_data = self.extract_poem_content(url)
        if poem_data:
            success = self.save_to_db(poem_data)
            if success:
                logger.success(f"成功爬取并保存: {poem_data['title']}")
            return poem_data
        else:
            logger.error(f"爬取失败: {url}")
            return None

def main():
    """测试函数"""
    spider = FinalEnhancedSpider()
    
    # 测试URL
    test_url = "https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx"
    
    result = spider.crawl_poem(test_url)
    if result:
        print("\n=== 最终爬取结果 ===")
        print(f"标题: {result['title']}")
        print(f"作者: {result['author']} ({result['dynasty']})")
        print(f"内容长度: {len(result['content'])}")
        print(f"译文长度: {len(result['translation'])}")
        print(f"注释长度: {len(result['annotation'])}")
        print(f"赏析长度: {len(result['appreciation'])}")
        
        print(f"\n译文内容:\n{result['translation'][:200]}...")
        print(f"\n注释内容:\n{result['annotation'][:200]}...")
        
        print(f"\n译文HTML:\n{result['translation_html'][:300]}...")
        print(f"\n注释HTML:\n{result['annotation_html'][:300]}...")

if __name__ == "__main__":
    main()
