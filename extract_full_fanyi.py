"""
从专门的fanyi页面提取完整的译文和注释
"""
import requests
from bs4 import BeautifulSoup
from loguru import logger

def extract_full_fanyi_content():
    """提取完整的译文和注释内容"""
    
    # 专门的fanyi页面URL
    fanyi_url = "https://www.gushiwen.cn/shiwenv_fanyi_56757.aspx"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Referer': 'https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx'
    }
    
    try:
        response = requests.get(fanyi_url, headers=headers)
        response.raise_for_status()
        response.encoding = 'utf-8'
        
        logger.info(f"成功获取fanyi页面，长度: {len(response.text)}")
        
        # 保存页面用于分析
        with open('fanyi_page.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        soup = BeautifulSoup(response.text, 'lxml')
        
        # 查找页面标题确认是正确的页面
        title = soup.find('title')
        if title:
            logger.info(f"页面标题: {title.get_text()}")
        
        # 查找所有可能包含译文和注释的容器
        print("=== 查找译文和注释容器 ===")
        
        # 方法1: 查找fanyi相关的div
        fanyi_divs = soup.find_all('div', id=lambda x: x and 'fanyi' in x)
        logger.info(f"找到 {len(fanyi_divs)} 个fanyi div")
        
        for div in fanyi_divs:
            div_id = div.get('id')
            content = div.get_text().strip()
            logger.info(f"Fanyi Div {div_id}: {len(content)} 字符")
            print(f"\n【{div_id}】内容:")
            print(content[:300] + "..." if len(content) > 300 else content)
            print("-" * 50)
        
        # 方法2: 查找包含"译文"和"注释"的段落
        print("\n=== 查找包含译文和注释的段落 ===")
        
        translation_paragraphs = soup.find_all('p', string=lambda text: text and '译文' in text)
        annotation_paragraphs = soup.find_all('p', string=lambda text: text and '注释' in text)
        
        logger.info(f"找到 {len(translation_paragraphs)} 个译文段落")
        logger.info(f"找到 {len(annotation_paragraphs)} 个注释段落")
        
        # 方法3: 查找所有段落，分析内容
        all_paragraphs = soup.find_all('p')
        logger.info(f"页面总共有 {len(all_paragraphs)} 个段落")
        
        translation_content = ""
        annotation_content = ""
        translation_html = ""
        annotation_html = ""
        
        for i, p in enumerate(all_paragraphs):
            text = p.get_text().strip()
            html = str(p)
            
            if text.startswith('译文') and len(text) > 50:
                translation_content = text
                translation_html = html
                logger.info(f"找到完整译文段落 {i+1}: {len(text)} 字符")
                print(f"\n【完整译文】:")
                print(text)
                print(f"\n【译文HTML】:")
                print(html[:500] + "..." if len(html) > 500 else html)
                
            elif text.startswith('注释') and len(text) > 50:
                annotation_content = text
                annotation_html = html
                logger.info(f"找到完整注释段落 {i+1}: {len(text)} 字符")
                print(f"\n【完整注释】:")
                print(text)
                print(f"\n【注释HTML】:")
                print(html[:500] + "..." if len(html) > 500 else html)
        
        # 方法4: 查找主要内容区域
        print("\n=== 查找主要内容区域 ===")
        
        main_content_selectors = [
            '.sons',
            '.main',
            '.content',
            '#main',
            '#content'
        ]
        
        for selector in main_content_selectors:
            elements = soup.select(selector)
            if elements:
                logger.info(f"找到 {len(elements)} 个 {selector} 元素")
                for elem in elements:
                    content = elem.get_text().strip()
                    if '译文' in content and '注释' in content and len(content) > 200:
                        logger.success(f"在 {selector} 中找到完整内容: {len(content)} 字符")
                        print(f"\n【{selector} 完整内容】:")
                        print(content[:500] + "..." if len(content) > 500 else content)
        
        return {
            'translation_content': translation_content,
            'annotation_content': annotation_content,
            'translation_html': translation_html,
            'annotation_html': annotation_html
        }
        
    except Exception as e:
        logger.error(f"提取失败: {e}")
        return {}

def main():
    """主函数"""
    result = extract_full_fanyi_content()
    
    if result:
        print("\n" + "="*60)
        print("=== 提取结果总结 ===")
        print(f"译文长度: {len(result.get('translation_content', ''))}")
        print(f"注释长度: {len(result.get('annotation_content', ''))}")
        
        if result.get('translation_content'):
            print(f"\n译文内容:\n{result['translation_content'][:200]}...")
        
        if result.get('annotation_content'):
            print(f"\n注释内容:\n{result['annotation_content'][:200]}...")

if __name__ == "__main__":
    main()
