"""
使用Selenium的隐蔽爬虫
尝试绕过反爬虫检测
"""
import time
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from bs4 import BeautifulSoup
from loguru import logger


class SeleniumSpider:
    """Selenium隐蔽爬虫"""

    def __init__(self):
        self.driver = None

    def create_stealth_driver(self):
        """创建隐蔽的Chrome驱动"""
        chrome_options = Options()

        # 基本设置
        chrome_options.add_argument('--headless')  # 无头模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')

        # 反检测设置
        chrome_options.add_argument(
            '--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option(
            "excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 设置用户代理
        chrome_options.add_argument(
            '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

        # 设置窗口大小
        chrome_options.add_argument('--window-size=1366,768')

        # 禁用图片加载以提高速度
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2
        }
        chrome_options.add_experimental_option("prefs", prefs)

        try:
            self.driver = webdriver.Chrome(options=chrome_options)

            # 执行反检测脚本
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)

            self.driver.execute_script("""
                window.chrome = {
                    runtime: {},
                    loadTimes: function() {},
                    csi: function() {},
                    app: {}
                };
            """)

            logger.info("Chrome驱动创建成功")
            return True

        except Exception as e:
            logger.error(f"创建Chrome驱动失败: {e}")
            return False

    def simulate_human_behavior(self):
        """模拟人类行为"""
        try:
            # 随机滚动
            scroll_height = random.randint(100, 300)
            self.driver.execute_script(f"window.scrollTo(0, {scroll_height})")
            time.sleep(random.uniform(0.5, 1.0))

            # 滚动回顶部
            self.driver.execute_script("window.scrollTo(0, 0)")
            time.sleep(random.uniform(0.3, 0.8))

            # 随机移动鼠标
            actions = ActionChains(self.driver)
            actions.move_by_offset(random.randint(
                50, 200), random.randint(50, 200))
            actions.perform()
            time.sleep(random.uniform(0.2, 0.5))

        except Exception as e:
            logger.debug(f"模拟人类行为失败: {e}")

    def extract_full_content(self, url):
        """提取完整内容"""
        try:
            logger.info(f"开始访问页面: {url}")

            # 访问页面
            self.driver.get(url)

            # 等待页面加载
            time.sleep(5)

            # 检查页面标题
            title = self.driver.title
            logger.info(f"页面标题: {title}")

            # 检查是否被重定向到登录页面
            if '登录' in title or 'login' in title.lower():
                logger.error("页面被重定向到登录页面")
                return None

            # 等待特定元素加载完成
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CLASS_NAME, "contson"))
                )
                logger.info("页面主要内容已加载")
            except Exception as e:
                logger.warning(f"等待页面元素超时: {e}")

            # 再次检查页面状态
            current_url = self.driver.current_url
            logger.info(f"当前URL: {current_url}")

            if 'login' in current_url.lower() or 'denglu' in current_url.lower():
                logger.error("URL被重定向到登录页面")
                return None

            # 模拟人类行为
            self.simulate_human_behavior()

            # 查找并点击展开按钮
            expand_selectors = [
                "a[href*='fanyiShow']",
                "a[onclick*='fanyiShow']",
                "//a[contains(text(), '展开阅读全文')]",
                "//a[contains(text(), '展开全文')]",
                "//*[contains(@onclick, 'fanyiShow')]"
            ]

            clicked_count = 0

            for selector in expand_selectors:
                try:
                    if selector.startswith('//'):
                        # XPath选择器
                        elements = self.driver.find_elements(
                            By.XPATH, selector)
                    else:
                        # CSS选择器
                        elements = self.driver.find_elements(
                            By.CSS_SELECTOR, selector)

                    logger.info(f"选择器 '{selector}' 找到 {len(elements)} 个元素")

                    for element in elements:
                        try:
                            if element.is_displayed():
                                # 滚动到元素位置
                                self.driver.execute_script(
                                    "arguments[0].scrollIntoView(true);", element)
                                time.sleep(0.5)

                                # 使用ActionChains点击
                                actions = ActionChains(self.driver)
                                actions.move_to_element(
                                    element).click().perform()

                                clicked_count += 1
                                logger.info(f"成功点击第 {clicked_count} 个展开按钮")

                                # 等待内容加载
                                time.sleep(2)

                        except Exception as e:
                            logger.debug(f"点击元素失败: {e}")

                except Exception as e:
                    logger.debug(f"查找元素失败 '{selector}': {e}")

            logger.info(f"总共点击了 {clicked_count} 个展开按钮")

            # 等待所有内容加载完成
            time.sleep(3)

            # 获取页面源码
            page_source = self.driver.page_source
            logger.info(f"获取页面源码长度: {len(page_source)}")

            # 保存页面源码用于调试
            with open('selenium_page_source.html', 'w', encoding='utf-8') as f:
                f.write(page_source)

            return page_source

        except Exception as e:
            logger.error(f"提取内容失败: {e}")
            return None

    def parse_content(self, html_content, url):
        """解析页面内容"""
        if not html_content:
            return None

        soup = BeautifulSoup(html_content, 'lxml')

        result = {
            'source_url': url,
            'title': '',
            'author': '',
            'dynasty': '',
            'content': '',
            'translation': '',
            'translation_html': '',
            'annotation': '',
            'annotation_html': '',
            'appreciation': ''
        }

        try:
            # 提取标题
            title_elems = soup.find_all('h1')
            if title_elems:
                result['title'] = title_elems[0].get_text().strip()
                logger.info(f"提取标题: {result['title']}")

            # 提取作者和朝代
            author_elems = soup.find_all('p', class_='source')
            if author_elems:
                author_elem = author_elems[0]
                author_links = author_elem.find_all('a')
                if author_links:
                    result['author'] = author_links[0].get_text().strip().split()[
                        0]
                    if len(author_links) > 1:
                        dynasty_text = author_links[1].get_text().strip()
                        result['dynasty'] = dynasty_text.replace(
                            '〔', '').replace('〕', '')

            # 提取诗词内容
            content_divs = soup.find_all('div', class_='contson')
            if content_divs:
                content_div = content_divs[0]
                content_html = str(content_div)
                content_html = content_html.replace(
                    '<br>', '\n').replace('<br/>', '\n')
                content_soup = BeautifulSoup(content_html, 'lxml')
                result['content'] = content_soup.get_text().strip()

            # 提取译文和注释
            import re
            fanyi_divs = soup.find_all('div', id=re.compile(r'fanyi\d+'))
            for fanyi_div in fanyi_divs:
                paragraphs = fanyi_div.find_all('p')

                for p in paragraphs:
                    text = p.get_text().strip()
                    html = str(p)

                    if text.startswith('译文') and len(text) > len(result['translation']):
                        result['translation'] = text
                        result['translation_html'] = html
                        logger.info(f"提取译文长度: {len(text)}")

                    elif text.startswith('注释') and len(text) > len(result['annotation']):
                        result['annotation'] = text
                        result['annotation_html'] = html
                        logger.info(f"提取注释长度: {len(text)}")

            # 提取赏析
            shangxi_divs = soup.find_all('div', id=re.compile(r'shangxi\d+'))
            for shangxi_div in shangxi_divs:
                h2_elem = shangxi_div.find('h2')
                if h2_elem and '赏析' in h2_elem.get_text():
                    result['appreciation'] = shangxi_div.get_text().strip()
                    logger.info(f"提取赏析长度: {len(result['appreciation'])}")
                    break

            return result

        except Exception as e:
            logger.error(f"解析内容失败: {e}")
            return None

    def crawl_with_selenium(self, url):
        """使用Selenium爬取完整内容"""
        if not self.create_stealth_driver():
            return None

        try:
            # 提取内容
            html_content = self.extract_full_content(url)
            if not html_content:
                return None

            # 解析内容
            result = self.parse_content(html_content, url)
            return result

        finally:
            if self.driver:
                self.driver.quit()
                logger.info("浏览器已关闭")


def main():
    """测试函数"""
    spider = SeleniumSpider()

    test_url = "https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx"

    result = spider.crawl_with_selenium(test_url)

    if result:
        print("\n=== Selenium爬取结果 ===")
        print(f"标题: {result['title']}")
        print(f"作者: {result['author']} ({result['dynasty']})")
        print(f"译文长度: {len(result['translation'])}")
        print(f"注释长度: {len(result['annotation'])}")
        print(f"赏析长度: {len(result['appreciation'])}")

        if result['annotation']:
            print(f"\n完整注释内容:\n{result['annotation']}")

        if result['annotation_html']:
            print(f"\n注释HTML:\n{result['annotation_html']}")
    else:
        print("Selenium爬取失败")


if __name__ == "__main__":
    main()
