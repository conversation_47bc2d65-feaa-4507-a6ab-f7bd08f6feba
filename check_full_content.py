"""
检查是否有完整的注释内容
"""
import requests
from bs4 import BeautifulSoup

def check_full_annotation():
    """检查完整的注释内容"""
    url = "https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    response = requests.get(url, headers=headers)
    response.encoding = 'utf-8'
    
    soup = BeautifulSoup(response.text, 'lxml')
    
    # 查找fanyi div的完整内容
    fanyi_div = soup.find('div', id='fanyi56757')
    if fanyi_div:
        print("=== Fanyi Div 完整HTML ===")
        print(fanyi_div.prettify())
        
        print("\n=== 所有段落详细分析 ===")
        paragraphs = fanyi_div.find_all('p')
        for i, p in enumerate(paragraphs):
            print(f"\n段落 {i+1}:")
            print(f"HTML: {p}")
            print(f"文本: {p.get_text()}")
            print("-" * 50)
    
    # 检查是否有隐藏的div或其他容器
    print("\n=== 查找其他可能的注释容器 ===")
    
    # 查找所有包含"注释"的元素
    annotation_elements = soup.find_all(text=lambda text: text and '注释' in text)
    for i, elem in enumerate(annotation_elements):
        parent = elem.parent
        print(f"\n注释元素 {i+1}:")
        print(f"父元素: {parent.name}")
        print(f"父元素class: {parent.get('class', [])}")
        print(f"父元素id: {parent.get('id', '')}")
        print(f"内容: {elem.strip()[:100]}...")
    
    # 查找可能的AJAX URL或隐藏内容
    print("\n=== 查找可能的AJAX请求 ===")
    scripts = soup.find_all('script')
    for script in scripts:
        if script.string and ('56757' in script.string or 'fanyi' in script.string.lower()):
            print("发现相关脚本:")
            print(script.string[:500] + "..." if len(script.string) > 500 else script.string)
            print("-" * 50)

if __name__ == "__main__":
    check_full_annotation()
