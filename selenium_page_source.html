<html xmlns="http://www.w3.org/1999/xhtml" id="html"><head><meta http-equiv="Cache-Control" content="no-siteapp"><meta http-equiv="Cache-Control" content="no-transform "><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>
登录古诗文网
</title>
<script src="//hm.baidu.com/hm.js?9007fab6814e892d3020a64454da5a55"></script><script type="text/javascript">if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {window.location.href ="https://m.gushiwen.cn/app/DefaultGwd.aspx";} else {}
</script>
<link href="/user/reg.css?time=250519" rel="stylesheet" type="text/css">
<script src="/js/Code.js" type="text/javascript"></script>
<script src="/js/jquery-3.2.1.min.js" type="text/javascript"></script>
<style type="text/css">.hide-centerlogin{width:258px;height:250px;right:0;left:0;bottom:0;top:0;margin:auto;z-index:1;}.hide-centerlogin #formhead{width:220px;height:52px;margin:0;padding-top:12px;padding-bottom:-30px;border-top-left-radius:10px;border-top-right-radius:10px;background-color:#ffffff;}.hide-centerlogin #formhead-title{float:left;height:24px;color:#44b449;font-size:18px;font-weight:bold;margin-left:10px;margin-top:5px;}.hide-centerlogin #formbody{width:220px;height:210px;background-color:#1e1e1e;border-bottom-left-radius:10px;border-bottom-right-radius:10px;background-color:#ffffff;}.hide-centerlogin #formbody img{float:left;margin-left:10px;border-bottom-left-radius:10px;border-bottom-right-radius:10px;}</style>
<link href="/css/skin.css?time=250625" rel="stylesheet" type="text/css"><link href="/css/layui.css?time=250625" rel="stylesheet" type="text/css"><link href="/css/play.css?time=250625" rel="stylesheet" type="text/css">
<script src="/js/jquery-3.2.1.min.js" type="text/javascript"></script>
<script type="text/javascript">function getCookie(name) {var arr,reg =new RegExp("(^| )" + name + "=([^;]*)(;|$)");if (arr =document.cookie.match(reg))
return unescape(arr[2]);else
return null;}
 function selectLike(id) {document.getElementById('likeImg' + id).name =parseInt(document.getElementById('likeImg' + id).name) + 1;if (document.getElementById('likeImg' + id).name =='1') {var idsShigeLaiyo =getCookie('idsShiwen2017');if (idsShigeLaiyo !=null &&idsShigeLaiyo !='') {var ids =idsShigeLaiyo.split(',');for (var i =0;i < ids.length;i++) {if (ids[i] ==id) {document.getElementById('likeImg' + id).src ='https://ziyuan.guwendao.net/siteimg/shou-cangok.png';document.getElementById('likeImg' + id).alt ='已收藏';break;}
}
}
}
}
 function selectLikeMingju(id) {document.getElementById('likeImg' + id).name =parseInt(document.getElementById('likeImg' + id).name) + 1;if (document.getElementById('likeImg' + id).name =='1') {var idsShigeLaiyo =getCookie('idsMingju2017');if (idsShigeLaiyo !=null &&idsShigeLaiyo !='') {var ids =idsShigeLaiyo.split(',');for (var i =0;i < ids.length;i++) {if (ids[i] ==id) {document.getElementById('likeImg' + id).src ='https://ziyuan.guwendao.net/siteimg/shou-cangok.png';document.getElementById('likeImg' + id).alt ='已收藏';break;}
}
}
}
}
 function selectLikeAuthor(id) {document.getElementById('likeImg' + id).name =parseInt(document.getElementById('likeImg' + id).name) + 1;if (document.getElementById('likeImg' + id).name =='1') {var idsShigeLaiyo =getCookie('idsAuthor2017');if (idsShigeLaiyo !=null &&idsShigeLaiyo !='') {var ids =idsShigeLaiyo.split(',');for (var i =0;i < ids.length;i++) {if (ids[i] ==id) {document.getElementById('likeImg' + id).src ='https://ziyuan.guwendao.net/siteimg/shou-cangok.png';document.getElementById('likeImg' + id).alt ='已收藏';break;}
}
}
}
}
 function selectLikeGuwen(id) {document.getElementById('likeImg' + id).name =parseInt(document.getElementById('likeImg' + id).name) + 1;if (document.getElementById('likeImg' + id).name =='1') {var idsShigeLaiyo =getCookie('idsGuji2017');if (idsShigeLaiyo !=null &&idsShigeLaiyo !='') {var ids =idsShigeLaiyo.split(',');for (var i =0;i < ids.length;i++) {if (ids[i] ==id) {document.getElementById('likeImg' + id).src ='https://ziyuan.guwendao.net/siteimg/shou-cangok.png';document.getElementById('likeImg' + id).alt ='已收藏';break;}
}
}
}
}
</script>
<script>var _hmt =_hmt ||[];(function () {var hm =document.createElement("script");hm.src ="//hm.baidu.com/hm.js?9007fab6814e892d3020a64454da5a55";var s =document.getElementsByTagName("script")[0];s.parentNode.insertBefore(hm,s);})();</script>
<link rel="stylesheet" type="text/css" href="https://ziyuan.guwendao.net/huaci/graybox.css"><link rel="stylesheet" type="text/css" href="https://ziyuan.guwendao.net/huaci/blackdownbar.css"><link rel="stylesheet" type="text/css" href="https://ziyuan.guwendao.net/huaci/graybox.css"><link rel="stylesheet" type="text/css" href="https://ziyuan.guwendao.net/huaci/blackdownbar.css"></head>
<body onclick="closeshowBos()">
<div class="maintopbc" style=" height:45px; background:url(https://ziyuan.guwendao.net/siteimg/24jie/%e5%a4%a7%e6%9a%91small.jpg) top center no-repeat; background-size:cover;">
<div class="maintop" style="opacity:0.94;">
<div class="cont">
<div class="left">
<a href="/">古诗文网</a>
</div>
<div class="right">
<div class="son1">
<a style="margin-left:1px;" href="/">推荐</a>
<a href="/shiwens/">诗文</a>
<a href="/mingjus/">名句</a>
<a href="/authors/">作者</a>
<a href="/guwen/">古籍</a>
<a href="/user/collect.aspx" rel="nofollow">我的</a>
<a style="width:65px;" href="/app/" target="_blank">APP</a>
</div>
<div class="son2">
<div class="search">
<form action="/search.aspx" onsubmit="return selectSearch()" contenttype="text/html; charset=utf-8">
<input onkeydown="noajaxkeyUp()" oninput="goshowBos()" id="txtKey" name="value" type="text" value="" maxlength="40" autocomplete="off" style="height:25px; line-height:25px; float:left; padding-left:10px; width:255px; font-size:14px; clear:left; border:0px;">
<input type="submit" style="float:right; width:23px; height:23px; clear:right; margin-top:2px; margin-right:4px; background-image:url(https://ziyuan.guwendao.net/siteimg/docSearch230511.png); background-repeat:no-repeat; background-size:23px 23px; border:0px;cursor:pointer;" value="">
<input id="b" style="display:none;" type="text">
</form>
</div>
</div>
</div>
</div>
</div>
<div class="main3">
<div style="width:300px; float:right;">
<div id="box"></div>
</div>
</div>
</div>
<div class="container" id="container" style="display: none;">
<div class="audio-player-container" id="audioplayercontainer">
<div class="audio-player-controls">
<button id="prevButton"><img src="/img/play/prevButton.png"></button>
<button id="playPauseButton"><img src="/img/play/playPauseButton.png"></button>
<button id="nextButton"><img src="/img/play/nextButton.png"></button>
<div class="progress-container">
<div class="time-info">
<div class="time-infoleft">
<span class="timenamestr" id="nameStr">东北一枝花</span>
<span class="time-langsong" id="author">-张哈哈</span>
</div>
<div class="time-inforhgit">
<span class="time-start" id="currentTime">0:00</span> /
<span class="time-end" id="duration">0:00</span>
<span class="time-langsong" id="langsongspan">(朗诵：<span id="langsongauthor">琼花</span>)</span>
</div>
</div>
<div class="progressBackground" id="progressBackground"></div>
<div class="progress" id="progress"></div>
<div class="progressBall" id="progressBall"></div>
</div>
<div class="close-button" id="closeButton"><img src="/img/play/close.png"></div>
<button id="xunhuanButton"><img src="/img/play/listplay.png" id="currentModeIcon"></button>
<button id="beisuButton"><img src="/img/play/beisu.png"></button>
<div class="listButton">
<span class="payaaa">
<button id="listButton">
<img src="/img/play/list.png" alt="播放列表按钮">
</button>
<span id="palynum">12</span>
</span>
<div class="playlist-container" id="playlistcontainer">
<div class="playlist-header">
播放列表 <span class="right-icons">
<img src="/img/play/clear.png" class="icon-space" id="clear">
<img src="/img/play/playclose.png" class="icon-space" id="playclose">
</span>
</div>
<div class="playlist-wrapper" id="playlistWrapper">
<ul class="playlist" id="playlist"></ul>
</div>
</div>
</div>
<div class="volume-control">
<span id="volume"><img src="/img/play/volume.png"></span>
<input type="range" id="volumeControl" min="0" max="1" step="0.01" value="0.5" style="background: linear-gradient(to right, rgb(93, 97, 70) 50%, rgb(212, 209, 187) 50%); --slider-thumb-color: #5D6146;">
</div>
<div class="speed-controls" id="speedControls">
<ul class="speed-options" id="speedOptions">
<li data-value="0.25" class="list-item">0.25x</li>
<li data-value="0.5" class="list-item">0.5x</li>
<li data-value="0.75" class="list-item">0.75x</li>
<li data-value="1" class="list-item selected">1.0x</li>
<li data-value="1.25" class="list-item">1.25x</li>
<li data-value="1.5" class="list-item">1.5x</li>
<li data-value="2" class="list-item">2.0x</li>
</ul>
</div>
<div class="play-mode" id="playModeControls">
<ul class="play-options" id="playModeOptions">
<li data-value="listLoop" class="list-item selected"><img src="/img/play/listplay.png" class="icon">列表循环</li>
<li data-value="random" class="list-item"><img src="/img/play/random.png" class="icon">随机播放</li>
<li data-value="singleLoop" class="list-item"><img src="/img/play/singpaly.png" class="icon">单曲循环</li>
<li data-value="single" class="list-item"><img src="/img/play/sing.png" class="icon">单曲播放</li>
</ul>
</div>
</div>
<audio id="audioPlayer" style="display: none;">您的浏览器不支持<code>audio</code> 元素。</audio>
</div>
</div>
<form name="aspnetForm" method="post" action="./login.aspx?from=https%3a%2f%2fwww.gushiwen.cn%2fshiwenv_2cbd14d5ff81.aspx" id="aspnetForm" onsubmit="return testFrom()">
<div>
<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="/wEPDwUKLTU5OTg0MDIwNw8WAh4TVmFsaWRhdGVSZXF1ZXN0TW9kZQIBZGQGi0FCmPHMP+KelvQVsoBoqE2Axg==">
</div>
<div>
<input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="C93BE1AE">
</div>
<div class="mainreg"><span><b>快捷登录古诗文网</b></span>
</div>
<div class="mainreg2" style="margin-top:0px;">
<div id="leftLogin" style=" width:240px; height:300px; clear:both;margin-top:10px; float:left; margin-right:10px;">
<div id="gzherweima" class="overCurtain"></div>
<div class="hide-centerlogin">
<div id="formhead">
<div id="formhead-title">
<span style=" font-size:18px; margin-left:45px;">微信扫码登录</span>
<span style=" clear:both; color:#999999; font-weight:normal; font-size:12px;margin-left:51px;">首次需关注公众号</span>
</div>
</div>
<div id="formbody">
<img width="200" height="200" src="https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQFy8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyVWFPWVFqbGVkN2kxSmY1UE5FMXAAAgRPOIxoAwQAjScA" alt="">
</div>
</div>
<script>var timesRun =0;var intervalErweima =setInterval("selectErweima()","3000");function selectErweima() {timesRun =timesRun + 1;if (timesRun ==30) {$("#leftLogin").fadeOut("slow")
clearInterval(intervalErweima);intervalErweima =null;}
 if (getCookie('ticketStr') !=null &&getCookie('ticketStr').indexOf("|") >0) {var xmlhttp;if (window.XMLHttpRequest) {xmlhttp =new XMLHttpRequest();}
else {xmlhttp =new ActiveXObject("Microsoft.XMLHTTP");}
xmlhttp.onreadystatechange =function () {if (xmlhttp.readyState ==4 &&xmlhttp.status ==200) {if (xmlhttp.responseText =="1") {window.location.href ="https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx";}
}
}
xmlhttp.open("POST","/user/getEventLogin.aspx?scene_id=206583706",false);xmlhttp.send();}
}
</script>
</div>
<div class="mainreg2" style=" width:610px; float:left; margin:auto;margin-top:10px; clear:none;">
<span>账　号</span>
<input style="display:none;" type="text" id="from" name="from" value="https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx">
<input type="text" id="email" name="email" onblur="testEmail()" onfocus="onEmial()" style="height: 25px; width: 250px; font-size: 14px; line-height: 25px; padding-left: 5px; margin-left: 10px; float: left; color: rgb(153, 153, 153);" maxlength="50" value="邮箱 / 手机号">
<span id="emailNo" style="color:Red; margin-left:5px; display:none;">ㄨ Email或手机号格式不正确</span>
</div>
<div class="mainreg2" style=" width:610px; float:left;margin:auto;margin-top:10px; clear:none;">
<span>密　码</span>
<input type="password" id="pwd" name="pwd" onblur="testPwd()" onfocus="onPwd()" style=" height:25px; width:250px; font-size:14px; line-height:25px; padding-left:5px; margin-left:10px; float:left;" maxlength="20" value="">
<span id="pwdNo" style="color:Red; margin-left:5px; display:none;">ㄨ 长度为6~20个字符</span>
</div>
<div class="mainreg2" style=" width:610px; float:left;margin:auto;margin-top:10px; clear:none;">
<span>验证码</span>
<input type="text" id="code" name="code" style=" height:25px; width:52px; font-size:14px; line-height:25px; padding-left:5px; margin-left:10px; float:left;" maxlength="4" value="">
<img id="imgCode" style="cursor: pointer; float:left; margin-left:5px; margin-top:1px;" width="60" height="27" src="/RandCode.ashx" onclick="GetCodeImg()" alt="看不清，换一张">
</div>
<div class="mainreg2" style=" width:610px; float:left;margin:auto;margin-top:10px; clear:none;">
<span style="color:#E1E0C7;">　　　</span>
<a href="/user/findpwd.aspx?from=https%3a%2f%2fwww.gushiwen.cn%2fshiwenv_2cbd14d5ff81.aspx" style="float:left; margin-top:2px; margin-left:10px; font-size:12px; color:#676767;">忘记密码</a>
<a style="float:left; margin-top:1px; margin-left:10px; font-size:12px; color:#676767;">|</a>
<a href="/user/register.aspx?from=https%3a%2f%2fwww.gushiwen.cn%2fshiwenv_2cbd14d5ff81.aspx" style="float:left; margin-top:2px; margin-left:10px; font-size:12px; color:#676767;">马上注册</a>
</div>
<div class="mainreg2" style=" width:610px; float:left;margin:auto;margin-top:10px; clear:none;">
<span style="color:#E1E0C7;">　　　</span>
<input type="submit" id="denglu" name="denglu" style=" height:27px; border:0px; width:58px; font-size:14px; line-height:18px; margin-left:10px; float:left; cursor:pointer;background-color:#5d6146;color:#fff;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;" value="登录">
</div>
</div>
</form>
<script type="text/javascript">var zhanghao =document.getElementById('email');if (zhanghao.value =='邮箱 / 手机号') {zhanghao.style.color ='#999999';}
function testEmail() {if (zhanghao.value =='') {zhanghao.value ='邮箱 / 手机号';zhanghao.style.color ='#999999';}
var str =document.getElementById('email').value;var reg =/^([a-zA-Z0-9]+[_|\_|\.|\-]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.|\-]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;var reg2 =/^0?1[0-9][0-9]\d{8}$/i;if (reg.test(str) ||reg2.test(str)) {document.getElementById('emailNo').style.display ='none';}
else {document.getElementById('emailNo').style.display ='block';}
}
function onEmial() {zhanghao =document.getElementById('email');if (zhanghao.value =='邮箱 / 手机号') {zhanghao.value ='';zhanghao.style.color ='#0F0F0F';}
document.getElementById('emailNo').style.display ='none';}
function testPwd() {var str =document.getElementById('pwd').value;if (str.length < 6) {document.getElementById('pwdNo').style.display ='block';}
else {document.getElementById('pwdNo').style.display ='none';}
}
function onPwd() {document.getElementById('pwdNo').style.display ='none';}
function testFrom() {var str =document.getElementById('email').value;var reg =/^([a-zA-Z0-9]+[_|\_|\.|\-]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.|\-]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;var reg2 =/^0?1[0-9][0-9]\d{8}$/i;if (reg.test(str)==false &&reg2.test(str)==false) {document.getElementById('emailNo').style.display ='block';return false;}
str =document.getElementById('pwd').value;if (str.length < 6) {document.getElementById('pwdNo').style.display ='block';return false;}
}
</script>
<script defer="defer" src="/js/skin.js?time=250623" type="text/javascript"></script>
<div class="main4">
© 2025 <a href="/">古诗文网</a> | <a href="/shiwens/">诗文</a> | <a href="/mingjus/">名句</a> | <a href="/authors/">作者</a> | <a href="/guwen/">古籍</a> | <a href="/jiucuo.aspx?u=" target="_blank" rel="nofollow">纠错</a>
</div>
<script type="text/javascript">window.onload =function () {setIframeHeight(document.getElementById('external-frame'));};if (getCookie('gsw2017user') !=null &&getCookie('gsw2017user').split('|').length >=3) {var userDate =getCookie('gsw2017user').split('|')[2];var myDate =new Date(userDate);var now =new Date();if (myDate >=now) {for (var i =0;i < document.getElementsByClassName("abcd").length;i++) {document.getElementsByClassName("abcd")[i].style.display ='none';}
}
if (getCookie('gsw2017user').split('|').length >=4) {userDate =getCookie('gsw2017user').split('|')[3];myDate =new Date(userDate);if (myDate >=now) {for (var i =0;i < document.getElementsByClassName("abcd").length;i++) {document.getElementsByClassName("abcd")[i].style.display ='none';}
}
}
}
</script>
<script defer="defer" src="/js/play.js?time=250625" type="text/javascript"></script>
<script defer="defer" src="/js/layui.js?time=250625" type="text/javascript"></script>
<script defer="defer" src="/js/skin.js?time=250625" type="text/javascript"></script>
<script defer="defer" src="/js/listenerPlay.js?time=250625" type="text/javascript"></script>


<div class="aihuaci"><div class="aihuaciheader"><p></p><button id="aihuacihead">&nbsp;</button><span></span><button id="aihuaciclose"></button></div><div class="aihuacimain"><div class="aihuacimainloadbox"><img src="https://ziyuan.guwendao.net/siteimg/loading.gif" width="32" height="32"></div><iframe frameborder="0" id="aihuaciiframe" name="aihuaciiframe"></iframe><iframe frameborder="0" id="aihuaciadvert" name="aihuaciadvert" style="height: 0px; width: 100%;"></iframe></div><div class="aihuacibottom"><p></p><div><button id="aihuacibottombtn"></button></div><a href="//www.gushiwen.cn/" target="_blank">古诗文网</a><span></span></div><button class="aihuacisidebar" style="width: 5px; height: 100%; top: 0px; left: -2px; background: black; cursor: col-resize;"></button><button class="aihuacisidebar" style="width: 100%; height: 5px; top: -2px; left: 0px; background: black; cursor: row-resize;"></button><button class="aihuacisidebar" style="width: 5px; height: 100%; top: 0px; right: -2px; background: black; cursor: col-resize;"></button><button class="aihuacisidebar" style="width: 100%; height: 5px; left: 0px; bottom: -2px; background: black; cursor: row-resize;"></button><button class="aihuacisidepart" style="top: -2px; left: -2px; cursor: nw-resize;"></button><button class="aihuacisidepart" style="top: -2px; right: -2px; cursor: ne-resize;"></button><button class="aihuacisidepart" style="left: -2px; bottom: -2px; cursor: sw-resize;"></button><button class="aihuacisidepart" style="right: -2px; bottom: -2px; cursor: se-resize;"></button></div><div class="blankmask" style="display: none;"></div><form target="aihuaciiframe" method="post" action="/dict/fancha.aspx" accept-charset="utf-8" class="aihuacitollbar" style="width: 40px;"><input type="hidden" id="aihuacikeyword" name="z"><input type="hidden" name="url" value="www.gushiwen.cn/user/login.aspx?from=https%3A%2F%2Fwww.gushiwen.cn%2Fshiwenv_2cbd14d5ff81.aspx"><ul><li toolid="10" params="320,330,&amp;#37322;&amp;#20041;" style="background-position: 0px 0px;"><img src="" title="释义"><button type="button">释义</button></li></ul><span></span></form><div class="aihuaci"><div class="aihuaciheader"><p></p><button id="aihuacihead">&nbsp;</button><span></span><button id="aihuaciclose"></button></div><div class="aihuacimain"><div class="aihuacimainloadbox"><img src="https://ziyuan.guwendao.net/siteimg/loading.gif" width="32" height="32"></div><iframe frameborder="0" id="aihuaciiframe" name="aihuaciiframe"></iframe><iframe frameborder="0" id="aihuaciadvert" name="aihuaciadvert" style="height: 0px; width: 100%;"></iframe></div><div class="aihuacibottom"><p></p><div><button id="aihuacibottombtn"></button></div><a href="//www.gushiwen.cn/" target="_blank">古诗文网</a><span></span></div><button class="aihuacisidebar" style="width: 5px; height: 100%; top: 0px; left: -2px; background: black; cursor: col-resize;"></button><button class="aihuacisidebar" style="width: 100%; height: 5px; top: -2px; left: 0px; background: black; cursor: row-resize;"></button><button class="aihuacisidebar" style="width: 5px; height: 100%; top: 0px; right: -2px; background: black; cursor: col-resize;"></button><button class="aihuacisidebar" style="width: 100%; height: 5px; left: 0px; bottom: -2px; background: black; cursor: row-resize;"></button><button class="aihuacisidepart" style="top: -2px; left: -2px; cursor: nw-resize;"></button><button class="aihuacisidepart" style="top: -2px; right: -2px; cursor: ne-resize;"></button><button class="aihuacisidepart" style="left: -2px; bottom: -2px; cursor: sw-resize;"></button><button class="aihuacisidepart" style="right: -2px; bottom: -2px; cursor: se-resize;"></button></div><div class="blankmask" style="display: none;"></div><form target="aihuaciiframe" method="post" action="/dict/fancha.aspx" accept-charset="utf-8" class="aihuacitollbar" style="width: 40px;"><input type="hidden" id="aihuacikeyword" name="z"><input type="hidden" name="url" value="www.gushiwen.cn/user/login.aspx?from=https%3A%2F%2Fwww.gushiwen.cn%2Fshiwenv_2cbd14d5ff81.aspx"><ul><li toolid="10" params="320,330,&amp;#37322;&amp;#20041;" style="background-position: 0px 0px;"><img src="" title="释义"><button type="button">释义</button></li></ul><span></span></form></body></html>