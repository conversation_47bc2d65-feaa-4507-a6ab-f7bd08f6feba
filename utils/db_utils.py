"""
数据库工具函数
"""
from sqlalchemy import text
from models import SessionLocal, engine
from loguru import logger

def test_connection():
    """测试数据库连接"""
    try:
        db = SessionLocal()
        result = db.execute(text("SELECT version()"))
        version = result.fetchone()[0]
        logger.info(f"数据库连接成功: {version}")
        db.close()
        return True
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False

def clear_tables():
    """清空所有表数据"""
    try:
        db = SessionLocal()
        db.execute(text("TRUNCATE TABLE poems, authors, categories RESTART IDENTITY CASCADE"))
        db.commit()
        logger.info("所有表数据已清空")
        db.close()
        return True
    except Exception as e:
        logger.error(f"清空表数据失败: {e}")
        return False

def get_table_counts():
    """获取各表的记录数"""
    try:
        db = SessionLocal()
        tables = ['poems', 'authors', 'categories']
        counts = {}
        
        for table in tables:
            result = db.execute(text(f"SELECT COUNT(*) FROM {table}"))
            counts[table] = result.fetchone()[0]
            
        db.close()
        return counts
    except Exception as e:
        logger.error(f"获取表记录数失败: {e}")
        return {}

if __name__ == "__main__":
    # 测试数据库连接
    if test_connection():
        counts = get_table_counts()
        for table, count in counts.items():
            print(f"{table}: {count} 条记录")
