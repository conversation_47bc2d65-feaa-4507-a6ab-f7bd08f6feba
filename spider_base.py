"""
基础爬虫类
"""
import requests
import time
import random
from bs4 import BeautifulSoup
from fake_useragent import User<PERSON>gent
from loguru import logger
from config import SPIDER_CONFIG
from models import SessionL<PERSON><PERSON>, <PERSON><PERSON>, Author

class BaseSpider:
    """基础爬虫类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.delay = SPIDER_CONFIG['request_delay']
        self.setup_session()
        
    def setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)
        
    def get_page(self, url, **kwargs):
        """获取页面内容"""
        try:
            # 随机延迟
            time.sleep(self.delay + random.uniform(0, 1))
            
            response = self.session.get(url, **kwargs)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            logger.info(f"成功获取页面: {url}")
            return response
            
        except requests.RequestException as e:
            logger.error(f"获取页面失败 {url}: {e}")
            return None
            
    def parse_html(self, html_content):
        """解析HTML内容"""
        return BeautifulSoup(html_content, 'lxml')
        
    def save_to_db(self, data, model_class):
        """保存数据到数据库"""
        db = SessionLocal()
        try:
            if isinstance(data, list):
                for item in data:
                    db_item = model_class(**item)
                    db.add(db_item)
            else:
                db_item = model_class(**data)
                db.add(db_item)
                
            db.commit()
            logger.info(f"成功保存 {len(data) if isinstance(data, list) else 1} 条数据到数据库")
            
        except Exception as e:
            db.rollback()
            logger.error(f"保存数据到数据库失败: {e}")
        finally:
            db.close()

class GuShiWenSpider(BaseSpider):
    """古诗文网爬虫"""
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://www.gushiwen.cn"
        
    def get_poem_list(self, category_url):
        """获取诗词列表"""
        response = self.get_page(category_url)
        if not response:
            return []
            
        soup = self.parse_html(response.text)
        poem_links = []
        
        # 这里需要根据实际网站结构来解析
        # 示例代码，需要根据实际情况调整
        for link in soup.find_all('a', href=True):
            if '/shiwenv_' in link['href']:
                poem_links.append(self.base_url + link['href'])
                
        return poem_links
        
    def parse_poem_detail(self, poem_url):
        """解析诗词详情"""
        response = self.get_page(poem_url)
        if not response:
            return None
            
        soup = self.parse_html(response.text)
        
        # 这里需要根据实际网站结构来解析
        # 示例代码，需要根据实际情况调整
        poem_data = {
            'title': '',
            'author': '',
            'dynasty': '',
            'content': '',
            'translation': '',
            'annotation': '',
            'appreciation': '',
            'source_url': poem_url
        }
        
        return poem_data
        
    def run(self):
        """运行爬虫"""
        logger.info("开始运行古诗文网爬虫")
        
        # 示例：爬取唐诗分类
        category_url = f"{self.base_url}/gushi/tangshi.aspx"
        poem_links = self.get_poem_list(category_url)
        
        for poem_url in poem_links[:10]:  # 限制数量用于测试
            poem_data = self.parse_poem_detail(poem_url)
            if poem_data:
                self.save_to_db(poem_data, Poem)
                
        logger.info("爬虫运行完成")

if __name__ == "__main__":
    spider = GuShiWenSpider()
    spider.run()
