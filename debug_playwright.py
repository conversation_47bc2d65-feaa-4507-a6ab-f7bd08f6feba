"""
调试Playwright爬虫
"""
import asyncio
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup

async def debug_playwright():
    """调试Playwright获取的内容"""
    url = "https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # 显示浏览器
        context = await browser.new_context(
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = await context.new_page()
        
        try:
            # 访问页面
            await page.goto(url, wait_until='networkidle')
            print("成功访问页面")
            
            # 等待页面加载
            await page.wait_for_timeout(3000)
            
            # 保存初始HTML
            initial_html = await page.content()
            with open('debug_initial.html', 'w', encoding='utf-8') as f:
                f.write(initial_html)
            print("保存了初始HTML")
            
            # 查找展开按钮
            expand_buttons = await page.query_selector_all('a:has-text("展开阅读全文")')
            print(f"找到 {len(expand_buttons)} 个展开按钮")
            
            # 点击所有展开按钮
            for i, button in enumerate(expand_buttons):
                try:
                    is_visible = await button.is_visible()
                    if is_visible:
                        print(f"点击第 {i+1} 个按钮")
                        await button.click()
                        await page.wait_for_timeout(2000)
                except Exception as e:
                    print(f"点击按钮失败: {e}")
            
            # 保存点击后的HTML
            final_html = await page.content()
            with open('debug_final.html', 'w', encoding='utf-8') as f:
                f.write(final_html)
            print("保存了最终HTML")
            
            # 解析内容
            soup = BeautifulSoup(final_html, 'lxml')
            
            # 调试标题
            print("\n=== 调试标题 ===")
            h1_tags = soup.find_all('h1')
            print(f"找到 {len(h1_tags)} 个 h1 标签")
            for i, h1 in enumerate(h1_tags):
                print(f"H1 {i+1}: {h1.get_text().strip()}")
            
            # 调试译文区域
            print("\n=== 调试译文区域 ===")
            fanyi_divs = soup.find_all('div', id=lambda x: x and 'fanyi' in x)
            print(f"找到 {len(fanyi_divs)} 个 fanyi div")
            for i, div in enumerate(fanyi_divs):
                print(f"Fanyi {i+1} ID: {div.get('id')}")
                text = div.get_text()[:200]
                print(f"内容预览: {text}...")
            
            # 等待用户查看
            await page.wait_for_timeout(10000)
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_playwright())
