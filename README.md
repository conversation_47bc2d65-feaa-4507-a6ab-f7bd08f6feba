# 古诗文网爬虫项目 (GuShiWenCNSpider)

一个基于Python的古诗文网站爬虫项目，用于爬取和存储中国古典诗词数据。

## 项目特点

- 🐍 基于Python 3.13
- 🗄️ 使用PostgreSQL数据库存储
- 🕷️ 支持多种爬虫框架 (requests, scrapy, selenium, playwright)
- 📊 数据处理使用pandas和numpy
- 🔧 使用SQLAlchemy ORM
- 📝 完整的日志系统
- ⚙️ 灵活的配置管理

## 项目结构

```
GuShiWenCNSpider/
├── README.md              # 项目说明
├── requirements.txt       # 依赖包列表
├── .env                  # 环境变量配置
├── config.py             # 项目配置
├── models.py             # 数据库模型
├── spider_base.py        # 基础爬虫类
├── main.py               # 主程序入口
├── test_setup.py         # 环境测试脚本
├── logs/                 # 日志目录
├── spiders/              # 爬虫模块
├── utils/                # 工具函数
│   ├── __init__.py
│   └── db_utils.py       # 数据库工具
└── tests/                # 测试文件
```

## 安装和设置

### 1. 激活虚拟环境

```bash
source venv/bin/activate
```

### 2. 验证环境

```bash
python test_setup.py
```

### 3. 运行爬虫

```bash
python main.py
```

## 数据库结构

### poems 表 (诗词)
- id: 主键
- title: 诗词标题
- author: 作者
- dynasty: 朝代
- content: 诗词内容
- translation: 译文
- annotation: 注释
- appreciation: 赏析
- source_url: 来源URL

### authors 表 (作者)
- id: 主键
- name: 作者姓名
- dynasty: 朝代
- biography: 生平简介

### categories 表 (分类)
- id: 主键
- name: 分类名称
- description: 分类描述

## 已安装的第三方库

### 核心爬虫库
- **requests** - HTTP请求库
- **httpx** - 现代异步HTTP客户端
- **scrapy** - 专业爬虫框架
- **beautifulsoup4** - HTML解析
- **lxml** - 高性能XML/HTML解析
- **selenium** - 浏览器自动化
- **playwright** - 现代浏览器自动化

### 数据处理
- **pandas** - 数据分析
- **numpy** - 数值计算

### 数据库
- **psycopg2-binary** - PostgreSQL驱动
- **sqlalchemy** - ORM框架
- **alembic** - 数据库迁移

### 工具库
- **fake-useragent** - 随机User-Agent
- **python-dotenv** - 环境变量管理
- **loguru** - 现代日志库
- **tqdm** - 进度条

## 配置说明

### 环境变量 (.env)
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=gushiwen_spider
DB_USER=lee
DB_PASSWORD=

USER_AGENT=Mozilla/5.0...
REQUEST_DELAY=1
CONCURRENT_REQUESTS=8
```

### 数据库配置
项目已自动创建数据库 `gushiwen_spider`，使用用户 `lee` 连接。

## 使用示例

```python
from spider_base import GuShiWenSpider

# 创建爬虫实例
spider = GuShiWenSpider()

# 运行爬虫
spider.run()
```

## 开发建议

1. 在开发新的爬虫时，继承 `BaseSpider` 类
2. 使用 `loguru` 进行日志记录
3. 数据保存使用 `save_to_db` 方法
4. 遵守网站的robots.txt和访问频率限制

## 注意事项

- 请遵守目标网站的使用条款
- 合理设置请求间隔，避免对服务器造成压力
- 定期备份数据库数据
- 在生产环境中使用更安全的数据库密码

## 许可证

本项目仅供学习和研究使用。
