from scrapy.spiders import CSVFeedSpider


class $classname(CSVFeedSpider):
    name = "$name"
    allowed_domains = ["$domain"]
    start_urls = ["$url"]
    #headers = ["id", "name", "description", "image_link"]
    #delimiter = "\t"

    # Do any adaptations you need here
    #def adapt_response(self, response):
    #    return response

    def parse_row(self, response, row):
        i = {}
        #i["url"] = row["url"]
        #i["name"] = row["name"]
        #i["description"] = row["description"]
        return i
