"""
测试环境设置
"""
import sys
import os
from loguru import logger


def test_imports():
    """测试所有依赖包是否正确安装"""
    logger.info("=== 测试依赖包导入 ===")

    packages = [
        'requests',
        'beautifulsoup4',
        'lxml',
        'pandas',
        'numpy',
        'sqlalchemy',
        'psycopg2',
        'fake_useragent',
        'python_dotenv',
        'loguru',
        'tqdm',
        'aiohttp',
        'pydantic',
        'yaml',
        'selenium',
        'playwright',
        'scrapy'
    ]

    success_count = 0
    for package in packages:
        try:
            if package == 'beautifulsoup4':
                import bs4
                logger.success(f"✓ {package} (bs4)")
            elif package == 'python_dotenv':
                from dotenv import load_dotenv
                logger.success(f"✓ {package}")
            elif package == 'fake_useragent':
                from fake_useragent import UserAgent
                logger.success(f"✓ {package}")
            else:
                __import__(package)
                logger.success(f"✓ {package}")
            success_count += 1
        except ImportError as e:
            logger.error(f"✗ {package}: {e}")

    logger.info(f"依赖包测试完成: {success_count}/{len(packages)} 成功")
    return success_count == len(packages)


def test_database():
    """测试数据库连接"""
    logger.info("=== 测试数据库连接 ===")

    try:
        from utils.db_utils import test_connection
        if test_connection():
            logger.success("✓ 数据库连接成功")
            return True
        else:
            logger.error("✗ 数据库连接失败")
            return False
    except Exception as e:
        logger.error(f"✗ 数据库测试出错: {e}")
        return False


def test_config():
    """测试配置文件"""
    logger.info("=== 测试配置文件 ===")

    try:
        from config import DATABASE_CONFIG, SPIDER_CONFIG, TARGET_SITES
        logger.success("✓ 配置文件加载成功")
        logger.info(f"数据库: {DATABASE_CONFIG['database']}")
        logger.info(f"目标网站: {list(TARGET_SITES.keys())}")
        return True
    except Exception as e:
        logger.error(f"✗ 配置文件测试失败: {e}")
        return False


def test_models():
    """测试数据库模型"""
    logger.info("=== 测试数据库模型 ===")

    try:
        from models import create_tables, Poem, Author, Category
        create_tables()
        logger.success("✓ 数据库表创建成功")
        return True
    except Exception as e:
        logger.error(f"✗ 数据库模型测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始环境测试...")

    tests = [
        ("依赖包导入", test_imports),
        ("配置文件", test_config),
        ("数据库连接", test_database),
        ("数据库模型", test_models),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                logger.success(f"{test_name} 测试通过")
            else:
                logger.error(f"{test_name} 测试失败")
        except Exception as e:
            logger.error(f"{test_name} 测试出错: {e}")

    logger.info(f"\n=== 测试结果 ===")
    logger.info(f"通过: {passed}/{total}")

    if passed == total:
        logger.success("🎉 所有测试通过！环境设置成功！")
        logger.info("你现在可以运行 'python main.py' 来启动爬虫")
    else:
        logger.error("❌ 部分测试失败，请检查环境配置")

    return passed == total


if __name__ == "__main__":
    main()
