"""
数据库模型定义
"""
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
from config import DATABASE_URL

Base = declarative_base()

class Poem(Base):
    """古诗词模型"""
    __tablename__ = 'poems'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(200), nullable=False, comment='诗词标题')
    author = Column(String(100), nullable=False, comment='作者')
    dynasty = Column(String(50), comment='朝代')
    content = Column(Text, nullable=False, comment='诗词内容')
    translation = Column(Text, comment='译文')
    annotation = Column(Text, comment='注释')
    appreciation = Column(Text, comment='赏析')
    source_url = Column(String(500), comment='来源URL')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    is_active = Column(Boolean, default=True, comment='是否有效')

class Author(Base):
    """作者模型"""
    __tablename__ = 'authors'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, unique=True, comment='作者姓名')
    dynasty = Column(String(50), comment='朝代')
    birth_year = Column(String(20), comment='出生年份')
    death_year = Column(String(20), comment='逝世年份')
    biography = Column(Text, comment='生平简介')
    source_url = Column(String(500), comment='来源URL')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

class Category(Base):
    """分类模型"""
    __tablename__ = 'categories'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, unique=True, comment='分类名称')
    description = Column(Text, comment='分类描述')
    parent_id = Column(Integer, comment='父分类ID')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

# 创建数据库引擎
engine = create_engine(DATABASE_URL, echo=False)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_tables():
    """创建所有表"""
    Base.metadata.create_all(bind=engine)
    print("数据库表创建成功！")

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

if __name__ == "__main__":
    create_tables()
