"""
尝试模拟AJAX请求获取完整的译文和注释
"""
import requests
import json
from loguru import logger

class AjaxSimulator:
    """AJAX请求模拟器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.setup_session()
        
    def setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': 'https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx'
        }
        self.session.headers.update(headers)
    
    def try_ajax_requests(self):
        """尝试各种可能的AJAX请求"""
        base_url = "https://www.gushiwen.cn"
        poem_id = "56757"
        token = "F66277E568A057FE"
        
        # 可能的AJAX端点
        possible_endpoints = [
            f"/ajax/fanyi.aspx?id={poem_id}&token={token}",
            f"/ajax/fanyi.aspx?id={poem_id}",
            f"/fanyi/{poem_id}.aspx",
            f"/fanyi.aspx?id={poem_id}&token={token}",
            f"/ajax/getfanyi.aspx?id={poem_id}&token={token}",
            f"/getfanyi.aspx?id={poem_id}",
            f"/shiwenv_fanyi_{poem_id}.aspx",
        ]
        
        for endpoint in possible_endpoints:
            url = base_url + endpoint
            logger.info(f"尝试请求: {url}")
            
            try:
                # GET请求
                response = self.session.get(url)
                logger.info(f"GET {url} - 状态码: {response.status_code}")
                
                if response.status_code == 200:
                    content = response.text
                    logger.info(f"响应长度: {len(content)}")
                    logger.info(f"响应内容预览: {content[:200]}...")
                    
                    # 检查是否包含更多的注释内容
                    if '伊周' in content and len(content) > 100:
                        logger.success(f"可能找到完整内容: {url}")
                        return url, content
                
                # POST请求
                post_data = {
                    'id': poem_id,
                    'token': token
                }
                response = self.session.post(url, data=post_data)
                logger.info(f"POST {url} - 状态码: {response.status_code}")
                
                if response.status_code == 200:
                    content = response.text
                    logger.info(f"POST响应长度: {len(content)}")
                    logger.info(f"POST响应内容预览: {content[:200]}...")
                    
                    if '伊周' in content and len(content) > 100:
                        logger.success(f"POST可能找到完整内容: {url}")
                        return url, content
                        
            except Exception as e:
                logger.debug(f"请求失败 {url}: {e}")
        
        return None, None
    
    def try_direct_fanyi_url(self):
        """尝试直接访问fanyi相关的URL"""
        poem_id = "56757"
        
        # 尝试直接访问可能的fanyi页面
        fanyi_urls = [
            f"https://www.gushiwen.cn/fanyi/{poem_id}.aspx",
            f"https://www.gushiwen.cn/fanyi{poem_id}.aspx",
            f"https://www.gushiwen.cn/shiwenv_fanyi_{poem_id}.aspx",
        ]
        
        for url in fanyi_urls:
            try:
                logger.info(f"尝试直接访问: {url}")
                response = self.session.get(url)
                logger.info(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    content = response.text
                    logger.info(f"响应长度: {len(content)}")
                    
                    if '伊周' in content:
                        logger.success(f"找到包含伊周的页面: {url}")
                        return url, content
                        
            except Exception as e:
                logger.debug(f"直接访问失败 {url}: {e}")
        
        return None, None
    
    def analyze_javascript_function(self):
        """分析JavaScript函数的实现"""
        # 获取原页面的JavaScript代码
        url = "https://www.gushiwen.cn/shiwenv_2cbd14d5ff81.aspx"
        response = self.session.get(url)
        
        # 查找fanyiShow函数的实现
        import re
        
        # 查找函数定义
        function_pattern = r'function\s+fanyiShow\s*\([^)]*\)\s*\{([^}]*)\}'
        matches = re.findall(function_pattern, response.text, re.DOTALL)
        
        if matches:
            logger.info("找到fanyiShow函数实现:")
            for match in matches:
                logger.info(match)
        
        # 查找可能的AJAX调用
        ajax_patterns = [
            r'\.ajax\s*\(\s*\{([^}]*)\}',
            r'XMLHttpRequest',
            r'fetch\s*\(',
            r'\.get\s*\(',
            r'\.post\s*\('
        ]
        
        for pattern in ajax_patterns:
            matches = re.findall(pattern, response.text, re.IGNORECASE)
            if matches:
                logger.info(f"找到可能的AJAX调用 ({pattern}):")
                for match in matches[:3]:  # 只显示前3个
                    logger.info(match[:200] + "..." if len(match) > 200 else match)

def main():
    """主函数"""
    simulator = AjaxSimulator()
    
    logger.info("=== 分析JavaScript函数 ===")
    simulator.analyze_javascript_function()
    
    logger.info("\n=== 尝试AJAX请求 ===")
    ajax_url, ajax_content = simulator.try_ajax_requests()
    
    if ajax_content:
        logger.success(f"通过AJAX找到内容: {ajax_url}")
        print(f"\n完整内容:\n{ajax_content}")
    else:
        logger.info("AJAX请求未找到完整内容")
    
    logger.info("\n=== 尝试直接访问fanyi页面 ===")
    fanyi_url, fanyi_content = simulator.try_direct_fanyi_url()
    
    if fanyi_content:
        logger.success(f"通过直接访问找到内容: {fanyi_url}")
        print(f"\n完整内容:\n{fanyi_content[:500]}...")

if __name__ == "__main__":
    main()
